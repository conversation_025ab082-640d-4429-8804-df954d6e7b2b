"""
概念和板块名称过滤器模块

该模块提供了过滤无意义概念和板块名称的功能，
可以被其他模块调用以清理概念和板块数据。

作者: 从 dynamic_gap_detector.py 中分离出来
功能: 过滤掉无意义的概念和板块名称，提升数据质量
"""


def filter_meaningful_concepts_and_sectors(concepts_or_sectors):
    """
    过滤掉无意义的概念和板块

    参数:
    - concepts_or_sectors: 概念或板块列表/字典

    返回:
    - 过滤后的概念或板块
    """
    # 使用统一的无意义概念集合
    meaningless_items = get_meaningless_items()

    if isinstance(concepts_or_sectors, dict):
        # 如果是字典（概念统计），过滤键
        return {k: v for k, v in concepts_or_sectors.items() if k not in meaningless_items and not _contains_tdx(k)}
    elif isinstance(concepts_or_sectors, list):
        # 如果是列表，过滤元素
        return [item for item in concepts_or_sectors if item not in meaningless_items and not _contains_tdx(item)]
    else:
        return concepts_or_sectors


def _contains_tdx(name):
    """
    检查概念或行业名称是否包含TDX

    参数:
    - name: 概念或行业名称

    返回:
    - bool: True表示包含TDX，False表示不包含
    """
    if not name:
        return False
    return 'TDX' in str(name).upper()


def get_meaningless_items():
    """
    获取无意义概念和板块的集合

    返回:
    - set: 无意义概念和板块的集合
    """
    return {
        '昨日涨停_含一字',
        '融资融券',
        '预盈预增',
        '含可转债',
        '转债标的',
        '央视50',
        '央视50_',  # 添加带下划线的变体
        '基金重仓',
        '微盘股',
        '创业板综',
        'QFII重仓',
        '央国企改革',
        '国企改革',
        '租售同权',
        '超级品牌',
        '投资时钟',
        '中证A100',
        '专精特新',
        '标准普尔',
        '低价股',
        '昨日涨停',
        '昨日触板',
        '一带一路',
        '高股息精选',
        '含H股',
        '通达信88',
        '含B股',
        '上证50',
        '上证50_',  # 新增
        '上证180',  # 新增
        'ST板块',
        '昨日连板_含一字',
        '昨日连板',
        '沪股通',
        '深股通',
        '港股通',
        'MSCI',
        '标普道琼斯',
        '富时罗素',
        '参股新三板',  # 添加参股新三板
        '证金持股',  # 新增
        '同花顺漂亮100',  # 新增
        '同花顺中特估100',  # 新增
        '预亏预减',  # 新增
        '机构重仓',  # 新增
        'MSCI中盘',
        '中小300',
        '中证500',  # 新增
        '深成500',  # 新增
        '贬值受益',  # 新增
        '小盘非融',  # 新增
        'AH股',  # 新增
        '扣非亏损',
        '非周期股',
        '小盘国企',
        '中证200',
        '上证380',
        '高市净率',
        '资金前排',
        '即将解禁',
        '中字头',
        '举牌',
        '成渝特区',
        '低安全分',
        '滨海新区',
        '拟减持',
        '员工持股',
        '上证180_',
        '高市盈率',
        '基金增仓',
        '中特估',
        '中证央企',
        '独家药品',
        '创业300',
        '破发行价',
        '最近情绪',
        '昨日上榜',
        '最近多板',
        '近期强势',
        '最近异动',
        '回购计划',
        '300非周',
        '国证成长',
        '中创100',
        '高分红股',
        '基金减仓',
        'TDX 可选',
        'TDX 建设',
        'TDX 金融',
        'TDX 制造',
        'TDX 公用',
        '送转潜力',
        '股权转让',
        '承诺不减',
        '中证回购',
        '壳资源',
        '户数增加',
        '昨高换手',
        '昨日较弱',
        'QFII新进',
        # 【新增：更多指数和技术性概念】
        '深证50',
        '深证100',
        '深证100R',
        '深证300',
        '深证红利',
        '深证成指',
        '深证价值',
        '深证治理',
        '深主板50',
        '沪深300',
        'HS300_',
        '银河99',
        '中证100',
        '中证A50',
        '中证A100',
        '中证红利',
        '国证红利',
        '国证价值',
        '国证治理',
        '富时A50',
        'MSCIA50',
        'MSCI中国',
        'MSCI成份',
        '银河99',
        '中华A80',
        '保险重仓',
        '北上重仓',
        '破净股',
        '破净资产',
        '低市盈率',
        '低市净率',
        '高股息股',
        '高应收款',
        '大盘股',
        '大盘价值',
        '周期股',
        '300周期',
        '300ESG',
        '持续增长',
        '珠三角',
        '深圳特区',
        '分析师指',
        '区块链50',
        'ST股',
        'TDX 消费',
        '创业小盘',
        '近期新高',
        '保险重仓',
        '国证价值',
        '周期股',
        '高股息股',
        '持续增长',
        'MSCI成份',
        '中证红利',
        '分析师指',
        '行业龙头',
        # 【新增：明确的无意义概念】
        '其他',
        '未知',
        '无',
        '暂无',
        '无概念',
        '无行业',
        '其它'
    }


def is_meaningful_concept(concept_name):
    """
    判断单个概念或板块是否有意义

    参数:
    - concept_name: 概念或板块名称

    返回:
    - bool: True表示有意义，False表示无意义
    """
    meaningless_items = get_meaningless_items()
    return concept_name not in meaningless_items and not _contains_tdx(concept_name)


def filter_concept_list(concept_list):
    """
    过滤概念列表，移除无意义的概念

    参数:
    - concept_list: 概念列表

    返回:
    - list: 过滤后的概念列表
    """
    if not concept_list:
        return []

    meaningless_items = get_meaningless_items()
    return [concept for concept in concept_list if concept not in meaningless_items and not _contains_tdx(concept)]


def filter_concept_dict(concept_dict):
    """
    过滤概念字典，移除无意义的概念键

    参数:
    - concept_dict: 概念字典

    返回:
    - dict: 过滤后的概念字典
    """
    if not concept_dict:
        return {}

    meaningless_items = get_meaningless_items()
    return {k: v for k, v in concept_dict.items() if k not in meaningless_items and not _contains_tdx(k)}


# 为了向后兼容，提供一个别名
filter_concepts = filter_meaningful_concepts_and_sectors


if __name__ == "__main__":
    # 测试代码
    test_concepts = [
        '人工智能', '昨日涨停', '芯片概念', '融资融券',
        '新能源汽车', '央视50', '医疗器械', 'ST板块',
        'TDX 信息', 'TDX 材料', 'tdx 消费', '养老概念'  # 新增TDX测试
    ]

    print("原始概念列表:", test_concepts)
    filtered = filter_meaningful_concepts_and_sectors(test_concepts)
    print("过滤后概念列表:", filtered)

    test_dict = {
        '人工智能': 5,
        '昨日涨停': 3,
        '芯片概念': 8,
        '融资融券': 2,
        'TDX 信息': 4,  # 新增TDX测试
        'TDX 材料': 6,  # 新增TDX测试
        '养老概念': 7
    }

    print("\n原始概念字典:", test_dict)
    filtered_dict = filter_meaningful_concepts_and_sectors(test_dict)
    print("过滤后概念字典:", filtered_dict)

    # 测试TDX过滤功能
    print("\n=== TDX过滤测试 ===")
    tdx_test_cases = [
        'TDX 信息',
        'TDX 材料',
        'tdx 消费',
        'TDX建设',
        '人工智能',
        '养老概念'
    ]

    for concept in tdx_test_cases:
        is_tdx = _contains_tdx(concept)
        is_meaningful = is_meaningful_concept(concept)
        print(f"  {concept}: 包含TDX={is_tdx}, 有意义={is_meaningful}")
