import pandas as pd
import os
import re
import sqlite3
from datetime import datetime, time, timedelta  # 【V4.0 修改】导入 timedelta 用于龙头锁定
from tabulate import tabulate
import logging
import warnings
import sys
from io import StringIO

# --- 导入你原来的核心逻辑函数 ---
# 确保 get_all_capital_flow_east.py 在同一个目录下
# 【V4.0 修改】确保导入了新的评分标准
from get_all_capital_flow_east_5_test import (
    get_date_folder,
    convert_to_float,
    format_amount,
    get_stock_type,
    _get_stock_board_map,
    find_breakouts,
    load_and_combine_data_scan,
    BUY_CONDITIONS_SCORE,
    BUY_SCORE_THRESHOLD,
    AM_END_TIME,
    PM_START_TIME,
    MAINLINE_SCORE_THRESHOLD,      # 确保导入
    POTENTIAL_SCORE_THRESHOLD,     # 确保导入
    _dynamic_theme_discovery,      # 【新增导入】
    generate_master_analysis_report # 【新增导入】
)

# 【V7.1 新增】盈亏计算模块导入
from profit_loss_calculator import (
    batch_calculate_profit_loss,
    format_profit_loss_display
)

# 【新增】定义开盘和收盘时间
AM_START_TIME = time(9, 15)  # 9:15 开盘时间
PM_END_TIME = time(15, 0)  # 15:00 收盘时间

# 忽略不必要的警告
warnings.filterwarnings("ignore", category=FutureWarning)

# --- 复盘配置 ---
# 数据根目录配置 - 用户可在此修改数据文件夹路径
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'  # 示例：绝对路径，用户可根据实际情况修改
# BASE_DATA_DIR = 'data'  # 备用示例：当数据文件夹在脚本同级目录时使用此设置

BACKTEST_DATE = '2025-07-31'  # <--- 在这里修改你想要复盘的日期！

# 【V7.1 新增】盈亏计算功能开关
ENABLE_PROFIT_LOSS_CALCULATION = True  # <--- 设置为 True 开启盈亏计算，False 关闭
# 注意：开启后会联网获取实时行情计算盈亏，可能增加运行时间

# --- 复盘配置结束 ---
PREVIOUS_SECTOR_DATA_BT = {}  # 存储: {'板块名称': {'inflow': value, 'rank': value}}

# 【V4.0 修改】定义与实时版本一致的龙头锁定分钟数
SECTOR_LEADER_LOCK_MINUTES = 30

# --- 【交易大师心法 V1.0 升级 · 回测版】主线识别与板块强度定义 ---
# 用于存储当前最强的板块列表
STRONG_SECTORS_LIST_BT = []
# 用于存储当前已确认的“主线”板块
MAINLINE_SECTOR_LIST_BT = []
# 用于追踪板块的持续强势表现
# 格式: {'板块名称': 连续进入强势候选的次数}
SECTOR_STRENGTH_TRACKER_BT = {}

# --- 核心判断阈值 (与实时代码保持一致) ---
MAINLINE_CANDIDATE_COUNT = 3
MAINLINE_PERSISTENCE_THRESHOLD = 5
POTENTIAL_CANDIDATE_RANGE_START = 4
POTENTIAL_CANDIDATE_RANGE_END = 15
POTENTIAL_BRANCH_COUNT = 5


# --- 【V6.0 核心升级：多因子评分阈值】 ---
MAINLINE_SCORE_THRESHOLD = 8    # 主线总分阈值
POTENTIAL_SCORE_THRESHOLD = 5   # 潜在支线总分阈值

# --- 【V7.0 报告驱动 · 核心升级：全新信号评分体系】 ---
# 基于《涨停股异动分析报告》的核心发现，构建多维度、分时段的评分标准
REPORT_DRIVEN_SCORE = {
    # --- A+级核心信号 (决定性因素) ---
    'MAINLINE_LEADER_EARLY': 10,  # 【主线龙头+早盘】: 10:00前，主线板块内资金排名前2的绝对龙头
    'ACCELERATION_MAINLINE': 8,   # 【主线+加速度】: 命中了主线板块，且触发了资金加速度信号
    'BIG_BUY_EARLY_MAINLINE': 8,  # 【主线+早盘大买单】: 10:00前，主线板块个股出现千万级以上大买单

    # --- A级核心信号 (强力加分项) ---
    'MAINLINE_STOCK': 5,          # 【主线身份】: 属于当前主线板块 (基础分)
    'SECTOR_LEADER_TOP1': 5,      # 【板块龙头】: 在任意强势板块中资金排名第1
    'FUND_FLOW_TOP_10': 4,        # 【资金流排名】: 个股资金流总排名进入市场前10
    'BIG_BUY_CONTINUOUS': 4,      # 【连续大买单】: 在3分钟内出现2次或以上大额买单
    'SECTOR_PERSISTENCE_HIGH': 3, # 【板块持续性】: 所属板块持续强势超过15分钟(计数>5)

    # --- B级辅助信号 (重要观察点) ---
    'STRONG_SECTOR_STOCK': 2,     # 【强势板块身份】: 属于支线或潜在强势板块
    'FUND_FLOW_TOP_20': 2,        # 【资金流排名】: 个股资金流总排名进入市场前20
    'BIG_BUY_SINGLE': 2,          # 【单次大买单】: 出现单次500万以上大买单
    'RANK_JUMP': 2,               # 【排名跃升】: 触发了rank_change排名异动
    'PRICE_BREAKOUT_STRONG': 2,   # 【价格突破】: 涨幅超过7%

    # --- C级观察信号 (环境与确认) ---
    'SECTOR_NET_INFLOW': 1,       # 【板块资金流入】: 所属板块整体为净流入
    'AFTERNOON_BREAKOUT': 1,      # 【午后异动】: 在13:30后首次出现显著拉升
    'CHIP_CONCENTRATION': 1       # 【筹码集中】: 超大单和主力流入，中小单流出 (沿用旧逻辑)
}

# 信号分级阈值 (与报告对应)
SIGNAL_THRESHOLDS = {
    'A+': 15,  # 大师级/A+级信号：多项核心信号共振，成功率极高
    'A': 12,   # 专业级/A级信号：具备明确的龙头相，成功率高
    'B': 8     # 进阶级/B级信号：值得重点关注的潜力股
}

# 时间窗口定义
EARLY_MORNING_END = time(10, 0)    # 早盘黄金时间结束
AFTERNOON_START = time(13, 30)     # 午后时间开始

# --- 【V4.0 修改】模拟与实时版本一致的全局状态变量

SECTOR_LEADER_LOCK_BT = {}  # V4.0 新增: 龙头锁定, 格式: {'板块名称': {'code': '龙头代码', 'time': sim_time}}
BREAKOUT_STOCKS_LIST_BT = []
PREVIOUS_RANK_DATA_BT = {}
FIRST_SIGNAL_TIMES_BT = {}

# --- 【V7.0 新增】瞬时信号追踪器 ---
stock_big_buy_tracker = {}  # 格式: {'代码': [时间1, 时间2, ...]} 用于追踪连续大单
processed_buy_signals_global = set()  # 全局已处理信号集合，避免重复信号

# --- 【新增】主线支线时间序列记录 ---
MAINLINE_TIMELINE_BT = []  # 记录主线支线变化的时间序列
# 日志配置类 (无修改)
class BacktestLogger:
    def __init__(self, backtest_date):
        self.backtest_date = backtest_date
        self.backtest_folder = os.path.join('backtest', backtest_date)
        os.makedirs(self.backtest_folder, exist_ok=True)
        self.full_day_log_file = os.path.join(self.backtest_folder, f'{backtest_date}_full_day.log')
        self.current_hour_log_file = None
        self.current_hour = None
        self.log_buffer = StringIO()
        self.log_format = '%(asctime)s - %(levelname)s - %(message)s'
        self.setup_full_day_logger()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        sys.stdout = LogCapture(self, 'INFO')
        sys.stderr = LogCapture(self, 'ERROR')

    def setup_full_day_logger(self):
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        full_day_handler = logging.FileHandler(self.full_day_log_file, encoding='utf-8')
        full_day_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(self.log_format)
        full_day_handler.setFormatter(formatter)
        logging.root.setLevel(logging.INFO)
        logging.root.addHandler(full_day_handler)

    def get_hour_from_time(self, current_time):
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S').time()
        elif isinstance(current_time, datetime):
            current_time = current_time.time()
        hour = current_time.hour
        minute = current_time.minute
        if hour == 9 and minute >= 30:
            return 9
        elif hour == 10 and minute < 30:
            return 9
        elif hour == 10 and minute >= 30:
            return 10
        elif hour == 11 and minute < 30:
            return 10
        elif hour == 13:
            return 13
        elif hour == 14:
            return 14
        else:
            if minute >= 30:
                return hour
            else:
                return hour - 1 if hour > 0 else 23

    def setup_hourly_logger(self, current_time):
        hour_segment = self.get_hour_from_time(current_time)
        if self.current_hour != hour_segment:
            self.current_hour = hour_segment
            if hour_segment == 9:
                time_range = "0930-1030"
            elif hour_segment == 10:
                time_range = "1030-1130"
            elif hour_segment == 13:
                time_range = "1300-1400"
            elif hour_segment == 14:
                time_range = "1400-1500"
            else:
                time_range = f"{hour_segment:02d}00-{(hour_segment + 1):02d}00"
            self.current_hour_log_file = os.path.join(self.backtest_folder, f'{self.backtest_date}_{time_range}.log')

    def log_message(self, level, message, current_time=None):
        if current_time:
            self.setup_hourly_logger(current_time)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"{timestamp} - {level} - {message}\n"
        if level == 'INFO':
            logging.info(message)
        elif level == 'ERROR':
            logging.error(message)
        elif level == 'WARNING':
            logging.warning(message)
        elif level == 'DEBUG':
            logging.debug(message)
        if self.current_hour_log_file:
            try:
                with open(self.current_hour_log_file, 'a', encoding='utf-8') as f:
                    f.write(formatted_message)
            except Exception as e:
                print(f"写入小时日志失败: {e}")

    def restore_stdout(self):
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr


class LogCapture:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level
        self.original_stream = sys.stdout if level == 'INFO' else sys.stderr

    def write(self, message):
        self.original_stream.write(message)
        self.original_stream.flush()
        if message.strip():
            self.logger.log_message(self.level, message.strip())

    def flush(self):
        self.original_stream.flush()


# 创建全局日志记录器
backtest_logger = BacktestLogger(BACKTEST_DATE)

# 【V4.0 修改】模拟与实时版本一致的全局状态变量
SECTOR_LEADER_LOCK_BT = {}  # V4.0 新增: 龙头锁定, 格式: {'板块名称': {'code': '龙头代码', 'time': sim_time}}
BREAKOUT_STOCKS_LIST_BT = []
PREVIOUS_RANK_DATA_BT = {}
FIRST_SIGNAL_TIMES_BT = {}

# --- 【新增】用于在函数间传递详细的板块分析结果 ---
df_analysis_global_bt = pd.DataFrame()


def _dynamic_theme_discovery(df_analysis, top_n=10, min_freq=2):
    """
    【交易大师V3.2 · 动态版】从Top N板块中动态发现主题。
    :param df_analysis: 包含板块评分的DataFrame
    :param top_n: 分析排名前N的板块
    :param min_freq: 一个词根至少出现几次才被认为是主题的一部分
    :return: 一个包含主题词和其总分的元组, e.g., (['AI', '智能'], 150)
    """
    if df_analysis.empty or len(df_analysis) < min_freq:
        return None, 0

    top_sectors = df_analysis.head(top_n)

    # 1. 提取所有板块名称中的潜在词根 (2个或3个字符的词)
    word_scores = {}
    word_counts = {}

    # 定义一些需要排除的通用词或无意义的词
    stop_words = {'概念', '行业', '服务', '开发', '研究', '技术', '应用', '设备', '制造', '材料'}

    for _, row in top_sectors.iterrows():
        name = row['板块名称']
        score = row['总分']

        # 提取所有长度为2和3的子字符串作为候选词根
        words = set()
        for i in range(len(name) - 1):
            words.add(name[i:i + 2])
        if len(name) > 2:
            for i in range(len(name) - 2):
                words.add(name[i:i + 3])

        # 累加分数和计数
        for word in words:
            if word in stop_words or word.isdigit():
                continue
            word_scores[word] = word_scores.get(word, 0) + score
            word_counts[word] = word_counts.get(word, 0) + 1

    if not word_scores:
        return None, 0

    # 2. 找到频率足够高、总分足够高的核心主题词
    candidate_themes = []
    for word, count in word_counts.items():
        if count >= min_freq:
            candidate_themes.append((word, word_scores[word]))

    if not candidate_themes:
        return None, 0

    # 按总分排序，选出最强的词根作为主题核心
    candidate_themes.sort(key=lambda x: x[1], reverse=True)

    # 3. 构建主题：将相关的、高分的词根聚合在一起
    main_theme_word, main_theme_score = candidate_themes[0]
    final_theme_words = [main_theme_word]
    final_theme_total_score = main_theme_score

    # 聚合其他与主词根相关的词
    for word, score in candidate_themes[1:]:
        # 如果一个词是主词的子串，或者主词是它的子串，就认为它们相关
        if word in main_theme_word or main_theme_word in word:
            if word not in final_theme_words:
                final_theme_words.append(word)

    # 返回聚合后的主题词列表和主词根的分数
    return final_theme_words, main_theme_score


def generate_master_analysis_report(df_analysis, mainline_threshold, potential_threshold):
    """
    【交易大师V3.2 · 动态共振版】根据评分结果，动态发现主题并生成分层报告。
    """
    if df_analysis.empty or 'type' not in df_analysis.columns:
        return "市场信号模糊，或分析数据格式不正确。"

    # 1. 动态发现市场主题
    theme_words, theme_score = _dynamic_theme_discovery(df_analysis, top_n=10, min_freq=3)

    # 2. 分别识别行业和概念的主线与支线
    mainline_df = df_analysis[df_analysis['总分'] >= mainline_threshold]
    potential_df = df_analysis[
        (df_analysis['总分'] >= potential_threshold) & (df_analysis['总分'] < mainline_threshold)]

    mainline_sectors = mainline_df['板块名称'].tolist()
    potential_sectors = potential_df['板块名称'].tolist()

    # 3. 组装报告
    report_lines = []
    report_lines.append(f"--- (判断标准: 主线≥{mainline_threshold}分 | 支线≥{potential_threshold}分) ---")

    # 报告绝对主线：基于动态发现的主题
    if theme_words:
        # 检查最强的行业和最强的概念是否都与动态主题相关
        top_industry = df_analysis[df_analysis['type'] == '行业'].iloc[0]['板块名称'] if not df_analysis[
            df_analysis['type'] == '行业'].empty else None
        top_concept = df_analysis[df_analysis['type'] == '概念'].iloc[0]['板块名称'] if not df_analysis[
            df_analysis['type'] == '概念'].empty else None

        is_resonant = False
        if top_industry and top_concept:
            # 如果最强的行业或概念的名称中包含任何一个主题词，就认为是共振
            industry_match = any(word in top_industry for word in theme_words)
            concept_match = any(word in top_concept for word in theme_words)
            # 更宽松的共振判断：只要最强的行业和概念都出现在高分主线板块列表里即可
            if top_industry in mainline_sectors and top_concept in mainline_sectors:
                is_resonant = True

        if is_resonant:
            report_lines.append(f"【绝对主线(体魂共振)】: {', '.join(theme_words)}")
            report_lines.append(
                f"  解读: 市场最强合力涌现。最强行业({top_industry})与最强概念({top_concept})均指向同一核心。")
        else:
            report_lines.append(f"【市场核心】: {', '.join(theme_words)} (动态识别)")
    elif mainline_sectors:
        report_lines.append(f"【主线】: {', '.join(mainline_sectors)}")
        report_lines.append(f"  解读: 市场存在单边强势板块，但未形成统一题材。")
    else:
        report_lines.append("【主线】: 主线模糊，市场呈现轮动或混战格局。")

    # 报告支线
    impulse_tool = [s for s in potential_sectors if '证券' in s or '期货' in s]
    if impulse_tool:
        report_lines.append(f"【脉冲/工具】: {', '.join(impulse_tool)} (市场情绪放大器)")

    rotational_branch = [s for s in potential_sectors if s not in impulse_tool]
    if rotational_branch:
        report_lines.append(f"【轮动支线】: {', '.join(rotational_branch)} (局部/试探性攻击)")

    # 附上详细评分表
    report_lines.append("\n--- 板块评分详情 (Top 15) ---")
    display_df = df_analysis.head(15).copy()
    for col in ['今日涨跌幅', '今日主力净流入-净额', '板块名称', 'type', '总分', '评分理由', '持续强势次数']:
        if col not in display_df.columns:
            display_df[col] = 'N/A'

    display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(
        lambda x: f"{x:.2f}%" if isinstance(x, (int, float)) else x)
    display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(
        lambda x: format_amount(x) if isinstance(x, (int, float)) else x)
    report_lines.append(
        tabulate(display_df[['板块名称', 'type', '总分', '评分理由', '今日主力净流入-净额', '持续强势次数']],
                 headers=['板块名称', '类型', '总分', '评分理由', '主力净流入', '持续强势'], tablefmt='psql',
                 showindex=False))

    return '\n'.join(report_lines)


def task_analyze_strong_sectors_backtest(current_industry_df, current_concept_df, all_timestamps, current_sim_time):
    """
    【V6.5 动态量能复盘版】与实时版本完全对齐的板块分析函数。
    """
    global STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, SECTOR_STRENGTH_TRACKER_BT, PREVIOUS_SECTOR_DATA_BT
    global df_analysis_global_bt

    try:
        # 1. 数据获取和预处理 (逻辑不变)
        all_sectors_list = []
        if current_industry_df is not None and not current_industry_df.empty:
            if '名称' in current_industry_df.columns:
                industry_data = current_industry_df[['名称', '今日涨跌幅', '今日主力净流入-净额']].copy()
                industry_data['type'] = '行业'
                all_sectors_list.append(industry_data)

        if current_concept_df is not None and not current_concept_df.empty:
            rename_map = {}
            if '行业' in current_concept_df.columns: rename_map['行业'] = '名称'
            if '行业-涨跌幅' in current_concept_df.columns: rename_map['行业-涨跌幅'] = '今日涨跌幅'
            if '净额' in current_concept_df.columns: rename_map['净额'] = '今日主力净流入-净额'
            concept_data = current_concept_df.rename(columns=rename_map)
            if '名称' in concept_data.columns and '今日主力净流入-净额' in concept_data.columns:
                concept_data['今日主力净流入-净额'] = concept_data['今日主力净流入-净额'] * 100000000
                concept_data['type'] = '概念'
                all_sectors_list.append(concept_data[['名称', '今日涨跌幅', '今日主力净流入-净额', 'type']])

        if not all_sectors_list:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()
            return

        all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称', 'type'])
        all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
        all_sectors_df['今日涨跌幅'] = all_sectors_df['今日涨跌幅'].apply(convert_to_float)
        all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
        all_sectors_df.dropna(subset=['名称', '今日涨跌幅', '今日主力净流入-净额'], inplace=True)
        all_sectors_df.sort_values(by='今日主力净流入-净额', ascending=False, inplace=True)
        all_sectors_df.reset_index(drop=True, inplace=True)
        all_sectors_df['rank'] = all_sectors_df.index + 1

        # 2. 计算“资金加速度”和识别“主线挑战者” (逻辑不变)
        all_sectors_df['inflow_delta'] = 0.0
        challenger_sectors = set()
        if PREVIOUS_SECTOR_DATA_BT:
            for index, row in all_sectors_df.iterrows():
                sector_name = row['名称']
                if sector_name in PREVIOUS_SECTOR_DATA_BT:
                    previous_data = PREVIOUS_SECTOR_DATA_BT[sector_name]
                    all_sectors_df.at[index, 'inflow_delta'] = row['今日主力净流入-净额'] - previous_data.get('inflow',
                                                                                                              0)
                    previous_rank = previous_data.get('rank', 999)
                    current_rank = row['rank']
                    if previous_rank > 20 and current_rank <= 10: challenger_sectors.add(sector_name)
                    if 2 <= current_rank <= 5:
                        prev_rank_inflow = \
                        all_sectors_df.loc[all_sectors_df['rank'] == current_rank - 1, '今日主力净流入-净额'].iloc[0]
                        current_gap = prev_rank_inflow - row['今日主力净流入-净额']
                        prev_sector_of_rank_N_minus_1 = [s for s, d in PREVIOUS_SECTOR_DATA_BT.items() if
                                                         d.get('rank') == current_rank - 1]
                        if prev_sector_of_rank_N_minus_1:
                            prev_gap = PREVIOUS_SECTOR_DATA_BT[prev_sector_of_rank_N_minus_1[0]].get('inflow',
                                                                                                     0) - previous_data.get(
                                'inflow', 0)
                            if prev_gap > 0 and current_gap < prev_gap * 0.5: challenger_sectors.add(sector_name)
        all_sectors_df.sort_values(by='inflow_delta', ascending=False, inplace=True)
        all_sectors_df['momentum_rank'] = range(1, len(all_sectors_df) + 1)

        # 3. 【V6.5 核心升级】动态计算“市场量能基准”
        positive_flow_df = all_sectors_df[all_sectors_df['今日主力净流入-净额'] > 0].copy()
        if positive_flow_df.empty:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()
            PREVIOUS_SECTOR_DATA_BT.clear()
            return

        total_positive_inflow = positive_flow_df['今日主力净流入-净额'].sum()
        soul_tier_inflow = total_positive_inflow * 0.10
        core_tier_inflow = total_positive_inflow * 0.05
        active_tier_inflow = total_positive_inflow * 0.02

        # 4. 【V6.5 核心升级】判断“资金断层”
        is_gap_effect = False
        gap_leader_name = None
        if len(positive_flow_df) >= 2:
            top1_inflow, top2_inflow = positive_flow_df['今日主力净流入-净额'].iloc[0], \
            positive_flow_df['今日主力净流入-净额'].iloc[1]
            if top2_inflow > 0 and (top1_inflow / top2_inflow) >= 1.8:
                is_gap_effect, gap_leader_name = True, positive_flow_df.iloc[0]['名称']

        # 5. 【V6.5 核心升级】对所有板块进行多因子评分
        analysis_results = []
        for index, row in all_sectors_df.iterrows():
            sector_name, inflow, rank, reasons, total_score = row['名称'], row['今日主力净流入-净额'], row[
                'rank'], [], 0

            # --- 新评分逻辑开始 ---
            strength_score = 0
            if is_gap_effect and sector_name == gap_leader_name:
                # 资金断层得分逻辑
                if inflow >= core_tier_inflow:
                    strength_score = 10;
                    reasons.append("主线真龙(10)")
                elif inflow >= active_tier_inflow:
                    strength_score = 5;
                    reasons.append("支线龙头(5)")
                else:
                    strength_score = 2;
                    reasons.append("局部偷袭(2)")
            else:
                # 市场容量 + 头部优势 得分逻辑
                capacity_score = 0
                if inflow >= soul_tier_inflow:
                    capacity_score = 5;
                    reasons.append("市场灵魂(5)")
                elif inflow >= core_tier_inflow:
                    capacity_score = 3;
                    reasons.append("核心战场(3)")
                elif inflow >= active_tier_inflow:
                    capacity_score = 1;
                    reasons.append("活跃板块(1)")

                head_position_score = 0
                if rank <= 3 and capacity_score > 0:
                    head_position_score = 2;
                    reasons.append("头部优势(2)")

                strength_score = capacity_score + head_position_score

            if inflow >= active_tier_inflow or strength_score > 0:
                persistence_score = 0
                continuity_count = SECTOR_STRENGTH_TRACKER_BT.get(sector_name, 0)
                if continuity_count >= 30:
                    persistence_score = 3; reasons.append("持续霸榜(3)")
                elif continuity_count >= 15:
                    persistence_score = 1; reasons.append("持续活跃(1)")

                momentum_score = 0
                if row['momentum_rank'] <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif row['momentum_rank'] <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")

                challenger_score = 3 if sector_name in challenger_sectors else 0
                if challenger_score > 0: reasons.append("主线挑战者(3)")

                total_score = strength_score + persistence_score + momentum_score + challenger_score
            else:
                momentum_score = 0
                if row['momentum_rank'] <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif row['momentum_rank'] <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")
                total_score = momentum_score

            if total_score > 0:
                analysis_results.append({
                    "板块名称": sector_name, "type": row['type'], "总分": total_score, "评分理由": ' + '.join(reasons),
                    "今日涨跌幅": row['今日涨跌幅'], "今日主力净流入-净额": inflow,
                    "持续强势次数": SECTOR_STRENGTH_TRACKER_BT.get(sector_name, 0)
                })

        # 6. 将评分结果保存到全局 (逻辑不变)
        df_analysis_global_bt = pd.DataFrame(analysis_results).sort_values(by="总分",
                                                                           ascending=False) if analysis_results else pd.DataFrame()

        # 7. 根据总分进行分类，并更新全局列表 (逻辑不变)
        if not df_analysis_global_bt.empty:
            mainline_df = df_analysis_global_bt[df_analysis_global_bt['总分'] >= MAINLINE_SCORE_THRESHOLD]
            potential_df = df_analysis_global_bt[(df_analysis_global_bt['总分'] >= POTENTIAL_SCORE_THRESHOLD) & (
                        df_analysis_global_bt['总分'] < MAINLINE_SCORE_THRESHOLD)]
            MAINLINE_SECTOR_LIST_BT = mainline_df['板块名称'].tolist()
            strong_sectors_set = set(MAINLINE_SECTOR_LIST_BT) | set(
                potential_df['板块名称'].tolist()) | challenger_sectors
            STRONG_SECTORS_LIST_BT = list(strong_sectors_set)
        else:
            STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT = [], []

        # 8. 更新持续性追踪器 (逻辑不变)
        current_strong_set = set(STRONG_SECTORS_LIST_BT)
        for sn in current_strong_set: SECTOR_STRENGTH_TRACKER_BT[sn] = SECTOR_STRENGTH_TRACKER_BT.get(sn, 0) + 1
        for sn in list(SECTOR_STRENGTH_TRACKER_BT.keys()):
            if sn not in current_strong_set: SECTOR_STRENGTH_TRACKER_BT[sn] = 0

        # 9. 更新上一分钟的状态数据 (逻辑不变)
        PREVIOUS_SECTOR_DATA_BT.clear()
        for _, row in all_sectors_df.iterrows():
            PREVIOUS_SECTOR_DATA_BT[row['名称']] = {'inflow': row['今日主力净流入-净额'], 'rank': row['rank']}

    except Exception as e:
        import traceback
        logging.error(f"复盘分析强势板块失败: {e}\n{traceback.format_exc()}")
        STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, df_analysis_global_bt = [], [], pd.DataFrame()


# 【新增】将此新函数添加到 generate_master_analysis_report 函数之前
def _dynamic_theme_discovery(df_analysis, top_n=10, min_freq=2):
    """
    【交易大师V3.2 · 动态版】从Top N板块中动态发现主题。
    :param df_analysis: 包含板块评分的DataFrame
    :param top_n: 分析排名前N的板块
    :param min_freq: 一个词根至少出现几次才被认为是主题的一部分
    :return: 一个包含主题词和其总分的元组, e.g., (['AI', '智能'], 150)
    """
    if df_analysis.empty or len(df_analysis) < min_freq:
        return None, 0

    top_sectors = df_analysis.head(top_n)

    # 1. 提取所有板块名称中的潜在词根 (2个或3个字符的词)
    word_scores = {}
    word_counts = {}

    # 定义一些需要排除的通用词或无意义的词
    stop_words = {'概念', '行业', '服务', '开发', '研究', '技术', '应用', '设备', '制造', '材料'}

    for _, row in top_sectors.iterrows():
        name = row['板块名称']
        score = row['总分']

        # 提取所有长度为2和3的子字符串作为候选词根
        words = set()
        for i in range(len(name) - 1):
            words.add(name[i:i + 2])
        if len(name) > 2:
            for i in range(len(name) - 2):
                words.add(name[i:i + 3])

        # 累加分数和计数
        for word in words:
            if word in stop_words or word.isdigit():
                continue
            word_scores[word] = word_scores.get(word, 0) + score
            word_counts[word] = word_counts.get(word, 0) + 1

    if not word_scores:
        return None, 0

    # 2. 找到频率足够高、总分足够高的核心主题词
    candidate_themes = []
    for word, count in word_counts.items():
        if count >= min_freq:
            candidate_themes.append((word, word_scores[word]))

    if not candidate_themes:
        return None, 0

    # 按总分排序，选出最强的词根作为主题核心
    candidate_themes.sort(key=lambda x: x[1], reverse=True)

    # 3. 构建主题：将相关的、高分的词根聚合在一起
    main_theme_word, main_theme_score = candidate_themes[0]
    final_theme_words = [main_theme_word]
    final_theme_total_score = main_theme_score

    # 聚合其他与主词根相关的词
    for word, score in candidate_themes[1:]:
        # 如果一个词是主词的子串，或者主词是它的子串，就认为它们相关
        if word in main_theme_word or main_theme_word in word:
            if word not in final_theme_words:
                final_theme_words.append(word)

    # 返回聚合后的主题词列表和主词根的分数
    return final_theme_words, main_theme_score

# 【最终替换版】将此函数完整地复制并替换掉 get_all_capital_flow_east.py 和 backtestv5_test.py 中的旧版本
def generate_master_analysis_report(df_analysis, mainline_threshold, potential_threshold):
    """
    【交易大师V3.2 · 动态共振版】根据评分结果，动态发现主题并生成分层报告。
    """
    if df_analysis.empty or 'type' not in df_analysis.columns:
        return "市场信号模糊，或分析数据格式不正确。"

    # 1. 动态发现市场主题
    theme_words, theme_score = _dynamic_theme_discovery(df_analysis, top_n=10, min_freq=3)

    # 2. 分别识别行业和概念的主线与支线
    mainline_df = df_analysis[df_analysis['总分'] >= mainline_threshold]
    potential_df = df_analysis[
        (df_analysis['总分'] >= potential_threshold) & (df_analysis['总分'] < mainline_threshold)]

    mainline_sectors = mainline_df['板块名称'].tolist()
    potential_sectors = potential_df['板块名称'].tolist()

    # 3. 组装报告
    report_lines = []
    report_lines.append(f"--- (判断标准: 主线≥{mainline_threshold}分 | 支线≥{potential_threshold}分) ---")

    # 报告绝对主线：基于动态发现的主题
    if theme_words:
        # 检查最强的行业和最强的概念是否都与动态主题相关
        top_industry = df_analysis[df_analysis['type'] == '行业'].iloc[0]['板块名称'] if not df_analysis[
            df_analysis['type'] == '行业'].empty else None
        top_concept = df_analysis[df_analysis['type'] == '概念'].iloc[0]['板块名称'] if not df_analysis[
            df_analysis['type'] == '概念'].empty else None

        is_resonant = False
        if top_industry and top_concept:
            # 如果最强的行业或概念的名称中包含任何一个主题词，就认为是共振
            industry_match = any(word in top_industry for word in theme_words)
            concept_match = any(word in top_concept for word in theme_words)
            # 更宽松的共振判断：只要最强的行业和概念都出现在高分主线板块列表里即可
            if top_industry in mainline_sectors and top_concept in mainline_sectors:
                is_resonant = True

        if is_resonant:
            report_lines.append(f"【绝对主线(体魂共振)】: {', '.join(theme_words)}")
            report_lines.append(
                f"  解读: 市场最强合力涌现。最强行业({top_industry})与最强概念({top_concept})均指向同一核心。")
        else:
            report_lines.append(f"【市场核心】: {', '.join(theme_words)} (动态识别)")
    elif mainline_sectors:
        report_lines.append(f"【主线】: {', '.join(mainline_sectors)}")
        report_lines.append(f"  解读: 市场存在单边强势板块，但未形成统一题材。")
    else:
        report_lines.append("【主线】: 主线模糊，市场呈现轮动或混战格局。")

    # 报告支线
    impulse_tool = [s for s in potential_sectors if '证券' in s or '期货' in s]
    if impulse_tool:
        report_lines.append(f"【脉冲/工具】: {', '.join(impulse_tool)} (市场情绪放大器)")

    rotational_branch = [s for s in potential_sectors if s not in impulse_tool]
    if rotational_branch:
        report_lines.append(f"【轮动支线】: {', '.join(rotational_branch)} (局部/试探性攻击)")

    # 附上详细评分表
    report_lines.append("\n--- 板块评分详情 (Top 15) ---")
    display_df = df_analysis.head(15).copy()
    for col in ['今日涨跌幅', '今日主力净流入-净额', '板块名称', 'type', '总分', '评分理由', '持续强势次数']:
        if col not in display_df.columns:
            display_df[col] = 'N/A'

    display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(
        lambda x: f"{x:.2f}%" if isinstance(x, (int, float)) else x)
    display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(
        lambda x: format_amount(x) if isinstance(x, (int, float)) else x)
    report_lines.append(
        tabulate(display_df[['板块名称', 'type', '总分', '评分理由', '今日主力净流入-净额', '持续强势次数']],
                 headers=['板块名称', '类型', '总分', '评分理由', '主力净流入', '持续强势'], tablefmt='psql',
                 showindex=False))

    return '\n'.join(report_lines)

def run_breakout_scan_backtest(all_day_df, current_time_for_scan):  # (无修改)
    """【复盘版】异动扫描函数"""
    global BREAKOUT_STOCKS_LIST_BT
    try:
        logging.info("--- [复盘] 开始扫描异动拉升股 ---")
        if all_day_df is None or all_day_df.empty:
            BREAKOUT_STOCKS_LIST_BT = []
            return

        if current_time_for_scan <= AM_END_TIME:
            period_name = "上午盘"
            df_period = all_day_df[all_day_df['timestamp'] <= current_time_for_scan]
        else:
            period_name = "下午盘"
            df_period = all_day_df[
                (all_day_df['timestamp'] >= PM_START_TIME) & (all_day_df['timestamp'] <= current_time_for_scan)]

        if df_period.empty:
            BREAKOUT_STOCKS_LIST_BT = []
            return

        _, breakout_codes = find_breakouts(df_period, period_name, all_day_df)
        BREAKOUT_STOCKS_LIST_BT = breakout_codes
        logging.info(f"当前时间点异动股: {BREAKOUT_STOCKS_LIST_BT}")
    except Exception as e:
        logging.error(f"复盘扫描异动股失败: {e}")
        BREAKOUT_STOCKS_LIST_BT = []


def generate_mainline_timeline_report(date_str, timeline_data):
    """
    【新增】生成主线支线时间序列报告
    """
    if not timeline_data:
        return "未记录到主线支线变化数据。"

    # 生成时间序列报告
    report_lines = []
    report_lines.append(f"====== {date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 主线支线时间序列报告 ======\n")

    # 按时间顺序输出所有变化
    for entry in timeline_data:
        time_str = entry['time']
        mainline_str = entry['mainline_str']
        potential_str = entry['potential_str']

        report_lines.append(f"【{time_str}】")
        report_lines.append(f"  资金主线: {mainline_str}")
        report_lines.append(f"  潜在支线: {potential_str}")
        report_lines.append("")  # 空行分隔

    # 生成变化统计
    report_lines.append("=" * 60)
    report_lines.append("【变化统计】")

    # 统计主线板块出现次数
    mainline_count = {}
    potential_count = {}

    for entry in timeline_data:
        for sector in entry['mainline']:
            mainline_count[sector] = mainline_count.get(sector, 0) + 1
        for sector in entry['potential']:
            potential_count[sector] = potential_count.get(sector, 0) + 1

    if mainline_count:
        report_lines.append("\n主线板块出现频次:")
        sorted_mainline = sorted(mainline_count.items(), key=lambda x: x[1], reverse=True)
        for sector, count in sorted_mainline:
            percentage = (count / len(timeline_data)) * 100
            report_lines.append(f"  {sector}: {count}次 ({percentage:.1f}%)")

    if potential_count:
        report_lines.append("\n支线板块出现频次:")
        sorted_potential = sorted(potential_count.items(), key=lambda x: x[1], reverse=True)
        for sector, count in sorted_potential:
            percentage = (count / len(timeline_data)) * 100
            report_lines.append(f"  {sector}: {count}次 ({percentage:.1f}%)")

    # 识别主线变化点
    report_lines.append("\n【主线变化节点】")
    prev_mainline = None
    change_points = []

    for entry in timeline_data:
        current_mainline = set(entry['mainline'])
        if prev_mainline is not None and current_mainline != prev_mainline:
            change_points.append({
                'time': entry['time'],
                'from': list(prev_mainline) if prev_mainline else [],
                'to': list(current_mainline)
            })
        prev_mainline = current_mainline

    if change_points:
        for change in change_points:
            from_str = ', '.join(change['from']) if change['from'] else "无主线"
            to_str = ', '.join(change['to']) if change['to'] else "无主线"
            report_lines.append(f"  {change['time']}: {from_str} → {to_str}")
    else:
        report_lines.append("  全天主线保持稳定，未发生变化")

    report_lines.append("=" * 60)

    return '\n'.join(report_lines)


def run_backtest(date_str):
    """ 【V7.1 报告驱动+盈亏分析复盘版】主复盘函数 """
    global PREVIOUS_RANK_DATA_BT, FIRST_SIGNAL_TIMES_BT
    global STRONG_SECTORS_LIST_BT, MAINLINE_SECTOR_LIST_BT, BREAKOUT_STOCKS_LIST_BT, SECTOR_LEADER_LOCK_BT
    global MAINLINE_TIMELINE_BT, stock_big_buy_tracker, processed_buy_signals_global
    global df_analysis_global_bt

    print(f"--- 开始对日期 {date_str} 进行盘后复盘 (V7.1 报告驱动+盈亏分析版) ---")
    logging.info(f"--- 开始对日期 {date_str} 进行盘后复盘 (V7.1 报告驱动+盈亏分析版) ---")
    backtest_logger.log_message('INFO', f"开始对日期 {date_str} 进行盘后复盘 (V7.1 报告驱动+盈亏分析版)")

    data_dir = os.path.join(BASE_DATA_DIR, date_str)
    if not os.path.isdir(data_dir):
        print(f"错误: 未找到日期 {date_str} 的数据文件夹！")
        return

    # 【V7.0 新增】初始化全局追踪器
    stock_big_buy_tracker.clear()
    processed_buy_signals_global.clear()

    # 1. 准备数据文件列表 (包含 sector_summary)
    rank_files = sorted([f for f in os.listdir(data_dir) if f.startswith('fund_flow_rank_') and f.endswith('.csv')])
    industry_sector_files = sorted(
        [f for f in os.listdir(data_dir) if 'sector_fund_flow' in f and f.endswith('.csv')
         and not f.startswith('concept_fund_flow') and not f.startswith('sector_summary')])
    concept_sector_files = sorted(
        [f for f in os.listdir(data_dir) if f.startswith('concept_fund_flow_') and f.endswith('.csv')])
    # 【核心修复】预加载所有sector_summary文件名，以备后用
    sector_summary_files = [f for f in os.listdir(data_dir) if f.startswith('sector_summary_') and f.endswith('.csv')]
    acceleration_files = sorted(
        [f for f in os.listdir(data_dir) if f.startswith('acceleration_signals_') and f.endswith('.csv')])
    big_deal_files = sorted([f for f in os.listdir(data_dir) if f.startswith('big_deal_') and f.endswith('.csv')])
    rank_change_files = sorted([f for f in os.listdir(data_dir) if f.startswith('rank_change_') and f.endswith('.csv')])

    if not rank_files:
        print("错误: 未找到任何 fund_flow_rank 数据文件！")
        return

    all_day_rank_df = load_and_combine_data_scan(data_dir)
    stock_board_map_df = _get_stock_board_map()
    all_timestamps = [datetime.strptime(f.split('_')[-1].replace('.csv', ''), '%H%M%S').time() for f in rank_files]

    # --- 预加载数据部分 (保持不变) ---
    acceleration_data_by_time = {}
    for acc_file in acceleration_files:
        try:
            ts = datetime.strptime(acc_file.split('_')[-1].replace('.csv', ''), '%H%M%S').time()
            df = pd.read_csv(os.path.join(data_dir, acc_file), encoding='utf-8-sig')
            if not df.empty: acceleration_data_by_time[ts] = df.to_dict('records')
        except Exception as e:
            logging.warning(f"预加载加速度文件 {acc_file} 失败: {e}")

    big_deal_data_by_time = {}
    for bd_file in big_deal_files:
        try:
            ts = datetime.strptime(bd_file.split('_')[-1].replace('.csv', ''), '%H%M%S').time()
            df = pd.read_csv(os.path.join(data_dir, bd_file), encoding='utf-8-sig')
            if not df.empty:
                mapped_records = []
                for _, row in df.iterrows():
                    mapped_records.append(
                        {'Stock_Code': row.get('股票代码', 'N/A'), 'Stock_Name': row.get('股票简称', 'N/A'),
                         'Deal_Amount': row.get('成交额', 0), 'Deal_Price': row.get('成交价格', 0),
                         'Deal_Volume': row.get('成交量', 0), 'Deal_Type': row.get('大单性质', 'N/A'),
                         'Change_Percent': row.get('涨跌幅', 0)})
                big_deal_data_by_time[ts] = mapped_records
        except Exception as e:
            logging.warning(f"预加载大单文件 {bd_file} 失败: {e}")

    rank_change_data_by_time = {}
    if rank_change_files:
        for rc_file in rank_change_files:
            try:
                ts = datetime.strptime(rc_file.split('_')[-1].replace('.csv', ''), '%H%M%S').time()
                df = pd.read_csv(os.path.join(data_dir, rc_file), encoding='utf-8-sig')
                if not df.empty: rank_change_data_by_time[ts] = df.to_dict('records')
            except Exception as e:
                logging.warning(f"预加载排名异动文件 {rc_file} 失败: {e}")

    all_buy_signals = []
    processed_buy_signals = set()
    all_acceleration_signals = []
    all_big_deal_signals = []
    all_rank_change_signals = []
    previous_mainline = None
    previous_potential = None
    MAINLINE_TIMELINE_BT = []

    # 2. 循环模拟每个时间点
    for i, rank_file in enumerate(rank_files):
        timestamp_str = rank_file.split('_')[-1].replace('.csv', '')
        current_sim_time = datetime.strptime(timestamp_str, '%H%M%S').time()

        if current_sim_time < AM_START_TIME or current_sim_time > PM_END_TIME:
            continue

        expired_sectors = [sector for sector, lock_info in SECTOR_LEADER_LOCK_BT.items() if
                           datetime.combine(datetime.min, current_sim_time) - datetime.combine(datetime.min, lock_info[
                               'time']) > timedelta(minutes=SECTOR_LEADER_LOCK_MINUTES)]
        for sector in expired_sectors:
            if sector in SECTOR_LEADER_LOCK_BT:
                del SECTOR_LEADER_LOCK_BT[sector]

        backtest_logger.setup_hourly_logger(current_sim_time)
        print(f"\n--- 模拟时间点: {current_sim_time} ({i + 1}/{len(rank_files)}) ---")

        try:
            current_rank_df = pd.read_csv(os.path.join(data_dir, rank_file), encoding='utf-8-sig')
            current_rank_df['代码'] = current_rank_df['代码'].astype(str).str.zfill(6)
            current_rank_df['今日涨跌幅'] = pd.to_numeric(current_rank_df['今日涨跌幅'], errors='coerce').fillna(0)
        except Exception as e:
            logging.error(f"读取或处理文件 {rank_file} 失败: {e}")
            continue

        def find_latest_file(file_list, current_time):
            relevant_file = None
            for f in sorted(file_list):
                try:
                    match = re.search(r'(\d{8})_(\d{6})', f) or re.search(r'_(\d{6})\.csv', f)
                    if not match: continue
                    f_ts_str = match.groups()[-1]
                    f_ts = datetime.strptime(f_ts_str, '%H%M%S').time()
                    if f_ts <= current_time:
                        relevant_file = f
                    else:
                        break
                except (ValueError, IndexError):
                    continue
            return relevant_file

        latest_industry_file = find_latest_file(industry_sector_files, current_sim_time)
        latest_concept_file = find_latest_file(concept_sector_files, current_sim_time)
        try:
            current_industry_df = pd.read_csv(os.path.join(data_dir, latest_industry_file), encoding='utf-8-sig',
                                              on_bad_lines='skip') if latest_industry_file else pd.DataFrame()
        except Exception:
            current_industry_df = pd.DataFrame()
        try:
            current_concept_df = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig',
                                             on_bad_lines='skip') if latest_concept_file else pd.DataFrame()
        except Exception:
            current_concept_df = pd.DataFrame()

        task_analyze_strong_sectors_backtest(current_industry_df, current_concept_df, all_timestamps, current_sim_time)
        run_breakout_scan_backtest(all_day_rank_df, current_sim_time)

        current_mainline = set(MAINLINE_SECTOR_LIST_BT)
        current_potential = set([s for s in STRONG_SECTORS_LIST_BT if s not in current_mainline])

        if previous_mainline is None or (
                current_mainline != previous_mainline or current_potential != previous_potential):
            print("\n" + "=" * 25 + f" 市场结构分析报告 @ {current_sim_time.strftime('%H:%M')} " + "=" * 25)
            master_report = generate_master_analysis_report(df_analysis_global_bt, MAINLINE_SCORE_THRESHOLD,
                                                            POTENTIAL_SCORE_THRESHOLD)
            print(master_report)
            print("=" * 85 + "\n")
            previous_mainline = current_mainline.copy()
            previous_potential = current_potential.copy()

        timeline_entry = {'time': current_sim_time.strftime('%H:%M:%S'), 'mainline': list(current_mainline),
                          'potential': list(current_potential),
                          'mainline_str': ', '.join(current_mainline) if current_mainline else "主线模糊",
                          'potential_str': ', '.join(current_potential) if current_potential else "无"}
        MAINLINE_TIMELINE_BT.append(timeline_entry)

        current_step_acceleration_signals = acceleration_data_by_time.get(current_sim_time, [])
        all_acceleration_signals.extend(current_step_acceleration_signals)
        current_step_big_deal_signals = big_deal_data_by_time.get(current_sim_time, [])
        all_big_deal_signals.extend(current_step_big_deal_signals)
        current_step_rank_change_signals = rank_change_data_by_time.get(current_sim_time, [])
        all_rank_change_signals.extend(current_step_rank_change_signals)

        # --- 【核心修复】开始：构建与实时脚本一致的【分析对象池】 ---
        buy_details_this_step = []
        stocks_to_process_list = []
        strong_sector_leaders = {} # 用于存储板块龙头信息，供后续评分使用

        # 1. 加入市场资金排名前20的股票
        stocks_to_process_list.append(current_rank_df.head(20))

        # 2. 加入各强势板块的龙头股 (从sector_summary文件中读取)
        if STRONG_SECTORS_LIST_BT:
            for sector_name in STRONG_SECTORS_LIST_BT:
                # 清理板块名称中的非法字符，以匹配文件名
                safe_sector_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                # 找到该板块对应的所有summary文件
                relevant_summary_files = [f for f in sector_summary_files if
                                          f.startswith(f"sector_summary_{safe_sector_name}_")]
                # 从中找到在当前模拟时间点之前最新的一个
                latest_summary_file = find_latest_file(relevant_summary_files, current_sim_time)
                if latest_summary_file:
                    try:
                        sector_leader_df = pd.read_csv(os.path.join(data_dir, latest_summary_file),
                                                       encoding='utf-8-sig')
                        if not sector_leader_df.empty:
                            # 将板块内资金排名前3的股票加入待处理列表
                            top_3_stocks = sector_leader_df.head(3)
                            stocks_to_process_list.append(top_3_stocks)
                            # 同时，缓存这些龙头股的代码，用于后续评分
                            top_3_codes = top_3_stocks['代码'].astype(str).str.zfill(6).tolist()
                            if top_3_codes:
                                strong_sector_leaders[sector_name] = top_3_codes
                    except Exception as e:
                        logging.warning(f"读取板块summary文件 {latest_summary_file} 失败: {e}")

        # 3. 合并并去重，形成最终的分析对象池
        combined_df = pd.concat(stocks_to_process_list, ignore_index=True)
        # 清理列名，因为不同来源的DF列名可能有空格
        combined_df.columns = [c.strip().replace(' ', '') for c in combined_df.columns]
        combined_df.drop_duplicates(subset=['代码'], inplace=True)
        processed_codes_this_step = set()
        # --- 【核心修复】结束 ---

        # 【核心修复】主分析循环现在迭代的是修复后的、更全面的 combined_df
        for _, row in combined_df.iterrows():
            stock_code = str(row['代码']).zfill(6)
            if stock_code in processed_codes_this_step: continue
            processed_codes_this_step.add(stock_code)

            stock_name = row['名称']
            main_net_abs = convert_to_float(row.get('今日主力净流入-净额', 0))
            main_ratio = convert_to_float(row.get('今日主力净流入-净占比', 0))
            super_net_abs = convert_to_float(row.get('今日超大单净流入-净额', 0))
            medium_net_abs = convert_to_float(row.get('今日中单净流入-净额', 0))
            small_net_abs = convert_to_float(row.get('今日小单净流入-净额', 0))
            change_percent = convert_to_float(row.get('今日涨跌幅', 0))
            price = convert_to_float(row.get('最新价', 0))

            stock_concepts = stock_board_map_df[stock_board_map_df['代码'] == stock_code]['概念名称'].tolist()
            related_sectors = [c for c in stock_concepts if c in STRONG_SECTORS_LIST_BT]

            is_locked_out = False
            for sector in related_sectors:
                if sector in SECTOR_LEADER_LOCK_BT:
                    locked_code = SECTOR_LEADER_LOCK_BT[sector]['code']
                    if stock_code != locked_code:
                        is_locked_out = True
                        break
            if is_locked_out:
                continue

            # --- 【V7.0 全新评分循环】基于多维度信号共振 ---
            score = 0
            reasons = []
            
            # 1. 初始化基础数据
            stock_concepts = stock_board_map_df[stock_board_map_df['代码'] == stock_code]['概念名称'].tolist()
            related_mainline_sectors = [c for c in stock_concepts if c in MAINLINE_SECTOR_LIST_BT]
            related_strong_sectors = [c for c in stock_concepts if c in STRONG_SECTORS_LIST_BT and c not in MAINLINE_SECTOR_LIST_BT]
            is_mainline = bool(related_mainline_sectors)
            is_strong = bool(related_strong_sectors)
            
            # 获取当前时间点的瞬时信号
            current_step_acceleration_signals_set = {s['Stock_Code'] for s in current_step_acceleration_signals}
            current_step_big_deal_signals_buy = [d for d in current_step_big_deal_signals if d.get('大单性质') == '买盘' or d.get('Deal_Type') == '买盘']
            current_step_rank_change_signals_set = {s['Stock_Code'] for s in current_step_rank_change_signals}
            
            # 时间窗口判断
            is_early_morning = current_sim_time < EARLY_MORNING_END
            is_afternoon = current_sim_time >= AFTERNOON_START
            
            # 全局排名 (假设combined_df已按资金流排序)
            overall_rank = row.get('rank', 999) if 'rank' in row else 999
            if overall_rank == 999:
                # 如果没有rank字段，尝试从原始数据中找到排名
                try:
                    overall_rank = current_rank_df[current_rank_df['代码'] == stock_code].index[0] + 1
                except:
                    overall_rank = 999
            
            # 2. 开始评分
            
            # --- 板块与龙头地位评分 ---
            is_sector_leader_top1 = False
            for sector, leaders in strong_sector_leaders.items():
                if stock_code == leaders[0]:
                    is_sector_leader_top1 = True
                    break

            if is_mainline:
                if is_early_morning and is_sector_leader_top1:
                    score += REPORT_DRIVEN_SCORE['MAINLINE_LEADER_EARLY']
                    reasons.append(f"主线龙头+早盘({REPORT_DRIVEN_SCORE['MAINLINE_LEADER_EARLY']}分)")
                else:
                    score += REPORT_DRIVEN_SCORE['MAINLINE_STOCK']
                    reasons.append(f"主线身份({REPORT_DRIVEN_SCORE['MAINLINE_STOCK']}分)")
            elif is_strong:
                score += REPORT_DRIVEN_SCORE['STRONG_SECTOR_STOCK']
                reasons.append(f"强势板块({REPORT_DRIVEN_SCORE['STRONG_SECTOR_STOCK']}分)")

            if is_sector_leader_top1:
                score += REPORT_DRIVEN_SCORE['SECTOR_LEADER_TOP1']
                reasons.append(f"板块龙头({REPORT_DRIVEN_SCORE['SECTOR_LEADER_TOP1']}分)")

            # --- 资金与排名评分 ---
            if overall_rank <= 10:
                score += REPORT_DRIVEN_SCORE['FUND_FLOW_TOP_10']
                reasons.append(f"资金流Top10({REPORT_DRIVEN_SCORE['FUND_FLOW_TOP_10']}分)")
            elif overall_rank <= 20:
                score += REPORT_DRIVEN_SCORE['FUND_FLOW_TOP_20']
                reasons.append(f"资金流Top20({REPORT_DRIVEN_SCORE['FUND_FLOW_TOP_20']}分)")

            # --- 瞬时异动信号评分 ---
            if stock_code in current_step_acceleration_signals_set and is_mainline:
                score += REPORT_DRIVEN_SCORE['ACCELERATION_MAINLINE']
                reasons.append(f"主线+加速度({REPORT_DRIVEN_SCORE['ACCELERATION_MAINLINE']}分)")
            
            if stock_code in current_step_rank_change_signals_set:
                score += REPORT_DRIVEN_SCORE['RANK_JUMP']
                reasons.append(f"排名跃升({REPORT_DRIVEN_SCORE['RANK_JUMP']}分)")

            # --- 大单买入评分 (核心) ---
            stock_big_deals = [d for d in current_step_big_deal_signals_buy if d.get('股票代码') == stock_code or d.get('Stock_Code') == stock_code]
            if stock_big_deals:
                # 更新追踪器
                if stock_code not in stock_big_buy_tracker: 
                    stock_big_buy_tracker[stock_code] = []
                stock_big_buy_tracker[stock_code].append(current_sim_time)
                
                # 计算总买入金额
                total_big_buy_amount = 0
                for d in stock_big_deals:
                    amount = d.get('成交额', 0) or d.get('Deal_Amount', 0)
                    if isinstance(amount, str):
                        amount = convert_to_float(amount)
                    total_big_buy_amount += amount * 10000  # 假设单位是万元
                
                # 连续大单判断 (3分钟内)
                recent_buys = [t for t in stock_big_buy_tracker[stock_code] 
                             if datetime.combine(datetime.min, current_sim_time) - datetime.combine(datetime.min, t) <= timedelta(minutes=3)]
                if len(recent_buys) >= 2:
                    score += REPORT_DRIVEN_SCORE['BIG_BUY_CONTINUOUS']
                    reasons.append(f"连续大买单({REPORT_DRIVEN_SCORE['BIG_BUY_CONTINUOUS']}分)")
                
                # 早盘千万级大单
                if is_early_morning and total_big_buy_amount > 10000000 and is_mainline:
                    score += REPORT_DRIVEN_SCORE['BIG_BUY_EARLY_MAINLINE']
                    reasons.append(f"主线早盘大买单({REPORT_DRIVEN_SCORE['BIG_BUY_EARLY_MAINLINE']}分)")
                # 普通大单
                elif total_big_buy_amount > 5000000:
                    score += REPORT_DRIVEN_SCORE['BIG_BUY_SINGLE']
                    reasons.append(f"单次大买单({REPORT_DRIVEN_SCORE['BIG_BUY_SINGLE']}分)")

            # --- 其他辅助评分 ---
            if change_percent > 7:
                score += REPORT_DRIVEN_SCORE['PRICE_BREAKOUT_STRONG']
                reasons.append(f"强势涨幅({REPORT_DRIVEN_SCORE['PRICE_BREAKOUT_STRONG']}分)")

            if is_afternoon and change_percent > 5:
                if stock_code not in processed_buy_signals_global:  # 确保是午后首次发力
                    score += REPORT_DRIVEN_SCORE['AFTERNOON_BREAKOUT']
                    reasons.append(f"午后异动({REPORT_DRIVEN_SCORE['AFTERNOON_BREAKOUT']}分)")

            # 筹码集中 (沿用旧逻辑)
            if (super_net_abs > 0 and medium_net_abs < 0 and small_net_abs < 0):
                score += REPORT_DRIVEN_SCORE['CHIP_CONCENTRATION']
                reasons.append(f"筹码集中({REPORT_DRIVEN_SCORE['CHIP_CONCENTRATION']}分)")

            # 3. 判断信号级别
            signal_level = None
            if score >= SIGNAL_THRESHOLDS['A+']:
                signal_level = 'A+'
            elif score >= SIGNAL_THRESHOLDS['A']:
                signal_level = 'A'
            elif score >= SIGNAL_THRESHOLDS['B']:
                signal_level = 'B'

            # 4. 触发买入信号判断
            if signal_level and stock_code not in processed_buy_signals_global:
                # 避免重复信号
                processed_buy_signals_global.add(stock_code)
                first_triggered_time = current_sim_time.strftime('%H:%M:%S')

                signal_info = {
                    'Time': f"{date_str} {first_triggered_time}", 
                    'Stock_Code': stock_code,
                    'Stock_Name': stock_name, 
                    'Price': price, 
                    'Change_Percent': change_percent,
                    'Main_Flow': main_net_abs, 
                    'Score': score,  # 新的评分
                    'Score_Reasons': ' + '.join(reasons),  # 新的评分明细
                    'Signal_Level': signal_level,  # 新增：信号级别
                    'Mainline_Sectors': ', '.join(related_mainline_sectors),
                    'Potential_Sectors': ', '.join(related_strong_sectors)
                }
                all_buy_signals.append(signal_info)
                
                # 新的详细信息格式
                detail_message = (f"【{signal_level}级信号】: {stock_code} ({stock_name}) | 总分:{score} | "
                                f"原因: {' + '.join(reasons)} | "
                                f"主线: {signal_info['Mainline_Sectors'] or '无'}")
                buy_details_this_step.append(detail_message)
            # --- 逻辑对齐结束 ---

        # --- (以下为报告打印部分，保持原样，无逻辑修改) ---
        if not current_rank_df.empty:
            print(f"\n--- 资金流向排行榜前20名 ---")
            display_df = current_rank_df.head(20)[
                ['代码', '名称', '今日主力净流入-净额', '今日主力净流入-净占比', '今日超大单净流入-净额',
                 '今日超大单净流入-净占比', '今日涨跌幅', '最新价']].copy()
            display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(
                lambda x: format_amount(float(x)) if pd.notna(x) else '0.00')
            display_df['今日超大单净流入-净额'] = display_df['今日超大单净流入-净额'].apply(
                lambda x: format_amount(float(x)) if pd.notna(x) else '0.00')
            display_df['今日主力净流入-净占比'] = display_df['今日主力净流入-净占比'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['今日超大单净流入-净占比'] = display_df['今日超大单净流入-净占比'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(
                lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%')
            display_df['最新价'] = display_df['最新价'].apply(lambda x: f"{float(x):.2f}" if pd.notna(x) else '0.00')
            print(tabulate(display_df, headers='keys', tablefmt='psql', showindex=False))
            print(f"--- 排行榜结束 ---\n")

        print("\n" + "=" * 20 + " 市场扫描报告 " + "=" * 20)
        report_msg = (
            f"报告摘要: 共扫描 {len(processed_codes_this_step)} 个股票 | "
            f"买入信号 {len(buy_details_this_step)} 个 | "
            f"卖出信号 0 个 | "
            f"大单追踪 {len(current_step_big_deal_signals)} 条。")
        print(report_msg)
        backtest_logger.log_message('INFO', report_msg, current_sim_time)

        if buy_details_this_step:
            print("\n--- 【V7.0】龙头信号详情 (分级) ---")
            backtest_logger.log_message('INFO', "【V7.0】龙头信号详情 (分级):", current_sim_time)
            # 按信号级别排序输出
            level_order = {'A+': 0, 'A': 1, 'B': 2}
            buy_details_this_step.sort(key=lambda x: level_order.get(x.split('级')[0][-2:], 3))
            for detail in buy_details_this_step:
                print(detail)
                backtest_logger.log_message('INFO', detail, current_sim_time)
        print("\n" + "=" * 20 + " 资金加速度排行榜 " + "=" * 20)
        print("--- (监控1分钟内主力资金流入的剧烈变化) ---")
        if current_step_acceleration_signals:
            table_data = []
            headers = ['代码', '名称', '当前排名', '涨跌幅', '激增金额', '激增前流入', '当前总流入']
            for signal in current_step_acceleration_signals:
                stock_code = signal.get('Stock_Code', 'N/A')
                stock_info_row = current_rank_df[current_rank_df['代码'] == stock_code]
                if not stock_info_row.empty:
                    current_rank = stock_info_row.index[0] + 1
                    table_data.append([
                        stock_code, signal.get('Stock_Name', 'N/A'), current_rank,
                        f"{signal.get('Change_Percent', 0):.2f}%",
                        format_amount(signal.get('Acceleration_Amount', 0)),
                        format_amount(signal.get('Previous_Main_Inflow', 0)),
                        format_amount(signal.get('Current_Main_Inflow', 0))
                    ])
            if table_data:
                print(tabulate(table_data, headers=headers, tablefmt='psql'))
        else:
            print("本次扫描未检测到资金加速度异动。")

        print("\n" + "=" * 20 + " 大单追踪详情 " + "=" * 20)
        print("--- (监控单笔成交额 >= 200万的大单) ---")
        if current_step_big_deal_signals:
            for signal in current_step_big_deal_signals:
                print(
                    f"{signal.get('Stock_Code', 'N/A')} ({signal.get('Stock_Name', 'N/A')}): {signal.get('Deal_Type', 'N/A')} {format_amount(signal.get('Deal_Amount', 0) * 10000)} "
                    f"@{signal.get('Deal_Price', 0):.2f}元 (涨跌幅: {signal.get('Change_Percent', 0):.2f}%)")
        else:
            print("本次扫描未检测到符合条件的大单交易。")
        print("\n" + "=" * 20 + " 排名异动排行榜 " + "=" * 20)
        print("--- (监控排名和占比的跃升) ---")
        if current_step_rank_change_signals:
            for signal in current_step_rank_change_signals:
                print(
                    f"{signal.get('Stock_Code', 'N/A')} ({signal.get('Stock_Name', 'N/A')}): [排名: {signal.get('Old_Rank', 'N/A')}→{signal.get('New_Rank', 'N/A')}; 主力净占比: {signal.get('Old_Main_Ratio', 0):.2f}%→{signal.get('New_Main_Ratio', 0):.2f}%]")
        else:
            print("本次扫描未检测到符合条件的排名异动")

        print("\n" + "=" * 20 + " 异动拉升排行榜 " + "=" * 20)
        try:
            if all_day_rank_df is not None and not all_day_rank_df.empty:
                if current_sim_time <= AM_END_TIME:
                    period_name = "上午盘"
                    df_period = all_day_rank_df[all_day_rank_df['timestamp'] <= current_sim_time]
                else:
                    period_name = "下午盘"
                    df_period = all_day_rank_df[(all_day_rank_df['timestamp'] >= PM_START_TIME) & (
                            all_day_rank_df['timestamp'] <= current_sim_time)]
                if not df_period.empty:
                    breakouts, _ = find_breakouts(df_period, period_name, all_day_rank_df)
                    if breakouts:
                        print(f"发现异动拉升股: {', '.join(breakouts.keys())}")
                    else:
                        print("暂未发现符合条件的异动拉升股票。")
        except Exception as e:
            logging.error(f"复盘整合异动拉升扫描报告时发生错误: {e}")
        print("=" * 65)

    # 3. 生成最终总结报告
    print("\n" + "=" * 60 + " 复盘总结报告 " + "=" * 60)
    analyzed_files = [f for f in rank_files if
                      AM_START_TIME <= datetime.strptime(f.split('_')[-1].replace('.csv', ''),
                                                         '%H%M%S').time() <= PM_END_TIME]
    final_summary_stats = f"""复盘日期: {date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}
            分析时间范围: {AM_START_TIME.strftime('%H:%M')}开盘至{PM_END_TIME.strftime('%H:%M')}收盘
            总数据时间点: {len(rank_files)} 个
            实际分析时间点: {len(analyzed_files)} 个
            买入信号总数: {len(all_buy_signals)} 个
            资金加速度信号总数: {len(all_acceleration_signals)} 个
            大单追踪信号总数: {len(all_big_deal_signals)} 个
            排名异动信号总数: {len(all_rank_change_signals)} 个"""
    print(final_summary_stats)
    print("=" * 140)
    backtest_logger.log_message('INFO', "复盘总结报告:")
    backtest_logger.log_message('INFO', final_summary_stats)
    print("\n" + "=" * 60 + " 主线支线时间序列分析 " + "=" * 60)
    timeline_report = generate_mainline_timeline_report(date_str, MAINLINE_TIMELINE_BT)
    print(timeline_report)
    timeline_file_path = os.path.join(data_dir, f'mainline_timeline_{date_str}.txt')
    with open(timeline_file_path, 'w', encoding='utf-8') as f:
        f.write(timeline_report)
    print(f"\n主线支线时间序列报告已保存至: {timeline_file_path}")
    backtest_logger.log_message('INFO', "主线支线时间序列报告:")
    backtest_logger.log_message('INFO', timeline_report)
    summary_file_path = os.path.join(data_dir, f'backtest_buy_summary_{date_str}.txt')

    if not all_buy_signals:
        print("\n--- 复盘完成，未触发任何买入信号 ---")
        final_report_text = f"====== {date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 复盘统计报告 ======\n\n"
        final_report_text += final_summary_stats
        final_report_text += "\n\n结论: 当日市场未出现符合买入条件的龙头股信号。"
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write(final_report_text)
        print(final_report_text)
        print(f"复盘统计报告已保存至: {summary_file_path}")
        return

    all_buy_signals.sort(key=lambda x: x['Time'])
    # 【V7.0 修改】按信号级别和时间对最终结果进行排序
    level_map = {'A+': 0, 'A': 1, 'B': 2}
    all_buy_signals.sort(key=lambda x: (level_map.get(x.get('Signal_Level', 'B'), 3), x['Time']))

    # 【V7.1 新增】盈亏计算 - 受开关控制
    if ENABLE_PROFIT_LOSS_CALCULATION:
        print("\n" + "=" * 60 + " 盈亏计算中 " + "=" * 60)
        print("正在获取买入信号股票的最新价格，计算当天盈亏...")
        print("注意：此功能需要网络连接，可能需要较长时间...")
        
        # 确定缓存日期 - 使用今天的日期作为缓存键
        cache_date = datetime.now().strftime('%Y-%m-%d')
        
        try:
            # 使用盈亏计算模块批量计算（启用缓存）
            pnl_results = batch_calculate_profit_loss(
                all_buy_signals, 
                delay_seconds=1, 
                use_cache=True, 
                cache_date=cache_date
            )
            
            # 将盈亏信息添加到买入信号中
            for i, signal in enumerate(all_buy_signals):
                if i < len(pnl_results):
                    pnl_info = pnl_results[i]
                    signal['PnL_Info'] = pnl_info
                    signal['PnL_Display'] = format_profit_loss_display(pnl_info)
            
            print(f"盈亏计算完成，共处理 {len(pnl_results)} 只股票")
            
        except Exception as e:
            logging.error(f"盈亏计算过程中发生错误: {e}")
            print(f"盈亏计算失败: {e}")
            # 为所有信号添加默认的盈亏信息
            for signal in all_buy_signals:
                signal['PnL_Display'] = "盈亏计算失败"
        
        print("=" * 140)
    else:
        print("\n" + "=" * 60 + " 盈亏计算已关闭 " + "=" * 60)
        print("提示：如需计算当天盈亏，请在文件头设置 ENABLE_PROFIT_LOSS_CALCULATION = True")
        print("=" * 140)
        
        # 为所有信号添加默认显示
        for signal in all_buy_signals:
            signal['PnL_Display'] = "未开启盈亏计算"

    report_title = f"====== {date_str[:4]}-{date_str[4:6]}-{date_str[6:8]} 买入信号【V7.1 报告驱动+盈亏分析】报告 ======\n\n"
    
    # 【V7.0 新增】信号级别统计
    level_stats = {}
    for signal in all_buy_signals:
        level = signal.get('Signal_Level', 'B')
        level_stats[level] = level_stats.get(level, 0) + 1
    
    stats_text = "信号级别统计: "
    for level in ['A+', 'A', 'B']:
        if level in level_stats:
            stats_text += f"{level}级{level_stats[level]}个  "
    
    enhanced_summary = final_summary_stats + f"\n{stats_text}"
    
    report_body = ""
    for signal in all_buy_signals:
        mainline_info = signal.get('Mainline_Sectors') or "无"
        potential_info = signal.get('Potential_Sectors') or "无"
        signal_level = signal.get('Signal_Level', 'B')
        pnl_display = signal.get('PnL_Display', '盈亏计算失败')
        
        signal_block = f"""--------------------------------------------------
触发时间: {signal['Time']}
股票信息: {signal['Stock_Name']} ({signal['Stock_Code']})
信号级别: 【{signal_level}】
信号评分: {signal['Score']}
评分明细: {signal['Score_Reasons']}
触发价格: {signal['Price']:.2f} (涨跌幅: {signal['Change_Percent']:.2f}%)
主力净流入: {format_amount(signal['Main_Flow'])}
所属主线: {mainline_info}
所属支线: {potential_info}
当天盈亏: {pnl_display}
"""
        report_body += signal_block
    final_report_text = report_title + enhanced_summary + "\n\n买入信号详情 (按级别和时间排序):\n" + report_body
    with open(summary_file_path, 'w', encoding='utf-8') as f:
        f.write(final_report_text)
    print("\n--- 复盘完成 ---")
    print(final_report_text)
    print(f"详细复盘报告已保存至: {summary_file_path}")
    backtest_logger.restore_stdout()

if __name__ == "__main__":
    try:
        run_backtest(BACKTEST_DATE)
    finally:
        backtest_logger.restore_stdout()


