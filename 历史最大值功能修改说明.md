# 历史最大值功能修改说明

## 修改概述

根据用户需求，为 `dynamic_gap_detector.py` 文件中的3个个股资金流入显示部分添加了历史最大值对比功能。

## 修改内容

### 1. 导入资金流查询工具

在文件开头添加了对 `fund_flow_query_basic` 模块的导入：

```python
# 导入资金流查询工具
try:
    import fund_flow_query_basic as fq
except ImportError:
    print("警告: 无法导入 fund_flow_query_basic，历史最大值功能将不可用")
    fq = None
```

### 2. 新增历史最大值检查函数

添加了 `check_historical_max_inflow()` 函数，用于检查股票是否大于历史最大资金流入：

**功能特点：**
- 查询指定股票的历史最大主力净流入净额和净占比
- 比较当前值与历史最大值
- 如果任意一个指标大于历史最大值，标记为"是"
- 支持回测安全（不会使用未来数据）
- 容错处理，如果查询失败会返回空标记

**使用的指标：**
- `main_net_inflow`: 主力净流入净额
- `main_net_ratio`: 主力净流入净占比

### 3. 修改的三个显示部分

#### 3.1 板块内部个股资金流入 (约第3608行)

**修改位置：** `analyze_sector_internal_flow` 函数中的表格生成部分

**修改内容：**
- 在生成表格前调用 `check_historical_max_inflow()` 检查历史最大值
- 在表格中添加"大于历史资金流入"列
- 表头从 `['排名', '股票名称', '主力净流入']` 更新为 `['排名', '股票名称', '主力净流入', '大于历史资金流入']`

#### 3.2 涨停股池 Top (约第4236行)

**修改位置：** 主分析循环中的涨停股池显示部分

**修改内容：**
- 由于涨停股池数据结构不包含资金流入信息，添加了从个股资金流数据中匹配的逻辑
- 通过股票名称匹配，将资金流入数据添加到涨停股池数据中
- 调用 `check_historical_max_inflow()` 检查历史最大值
- 在表格中添加"大于历史资金流入"列
- 更新表头，添加"大于历史资金流入"列

#### 3.3 个股资金流入 Top 50 (约第4319行)

**修改位置：** 主分析循环中的个股资金流入显示部分

**修改内容：**
- 在处理显示数据前调用 `check_historical_max_inflow()` 检查历史最大值
- 在 `display_columns` 和 `headers` 中添加"大于历史资金流入"列
- 保持原有的所有功能（涨跌幅、创月新高标记等）

## 技术实现细节

### 数据库查询
- 使用 `fund_flow_query_basic.query_historical_max_value()` 函数
- 查询条件：`WHERE date < ?` 确保回测安全
- 支持批量查询多只股票
- 返回格式：`['stock_code', 'metric', 'historical_max_value']`

### 比较逻辑
- 分别查询主力净流入净额和净占比的历史最大值
- 当前值与历史最大值进行比较
- 任意一个指标超过历史最大值即标记为"是"
- 处理空值和异常情况

### 容错机制
- 如果无法导入查询工具，功能降级但不影响主程序运行
- 如果查询失败，返回空标记而不是错误
- 处理数据缺失和格式异常情况

## 测试验证

创建了 `test_historical_max_feature.py` 测试脚本，验证：

1. ✅ 模块导入正常
2. ✅ 历史最大值检查功能正常
3. ✅ 数据库查询功能正常
4. ✅ 能够正确添加"大于历史资金流入"列

## 使用效果

修改完成后，在运行 `dynamic_gap_detector.py` 时，以下三个部分的表格将显示新增的"大于历史资金流入"列：

1. **板块内部个股资金流入** - 显示板块内个股是否超过历史最大资金流入
2. **涨停股池 Top** - 显示涨停股票是否超过历史最大资金流入  
3. **个股资金流入 Top 50** - 显示资金流入排名前50的股票是否超过历史最大值

当股票的当前主力净流入净额或净占比任意一个超过历史最大值时，该列将显示"是"，否则显示空白。

## 注意事项

1. 需要确保 `fund_flow.db` 数据库文件存在且包含历史数据
2. 数据库路径配置在 `fund_flow_query_basic.py` 中的 `DB_PATH` 变量
3. 功能依赖于 `daily_fund_flow` 表的数据完整性
4. 回测模式下确保不会使用未来数据
