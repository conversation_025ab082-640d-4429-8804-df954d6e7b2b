# 绝对主线/立即开火信号输出功能说明

## 功能概述

本次修改为 `dynamic_gap_detector.py` 添加了专门的"绝对主线/立即开火"信号输出功能，确保所有达到最高战术评级的信号都能按时间顺序完整输出到专用日志文件中。

## 修改内容

### 1. 新增日志文件类型

- **文件名格式**: `fire_signals_YYYYMMDD.txt` (主日志文件)
- **按小时分割**: `fire_signals_YYYYMMDD_HH-HH.txt` (按小时分割的日志文件)
- **存储位置**: `log/` 目录下

### 2. 信号识别标准

系统会自动识别以下条件的信号并输出到专用日志：
- **战术评级**: 包含 "绝对主线/立即开火" 字样的信号
- **战术评分**: ≥15分的信号
- **信号类型**: 包括但不限于：
  - 历史突破信号（二次点火、横空出世、突破强化等）
  - 盘口突袭信号

### 3. 输出格式

每个"绝对主线/立即开火"信号包含以下详细信息：

```
🎯 [信号类型]: 【股票名称】
   时间: HH:MM:SS
   排名: [当前排名]
   资金: [主力净流入金额]
   周期: [突破周期]（如适用）
   异动次数: [盘口异动次数]（盘口突袭信号）
   评分: [综合评分]
   战术分: [战术评分]
   主线分: [主线强度分]
   评级: [评级等级]
   理由: [评分理由]
   解读: [大师解读]
```

### 4. 时间顺序保证

- 信号按照检测时间顺序输出
- 每个时间点的信号都有明确的时间戳
- 支持按小时分割，便于查看特定时间段的信号

## 使用方法

### 运行主程序

```bash
python dynamic_gap_detector.py
```

程序运行后会自动：
1. 创建 `fire_signals_YYYYMMDD.txt` 日志文件
2. 在检测到"绝对主线/立即开火"信号时自动写入
3. 按时间顺序记录所有符合条件的信号

### 查看日志文件

日志文件位置：
- 主日志：`log/fire_signals_YYYYMMDD.txt`
- 按小时分割：`log/fire_signals_YYYYMMDD_HH-HH.txt`

### 测试功能

运行测试脚本验证功能：

```bash
python test_fire_signals.py
```

## 日志文件示例

```
🔥🔥🔥 时间点: 14:30:00 - 绝对主线/立即开火信号 🔥🔥🔥
检测到 1 个绝对主线/立即开火信号
================================================================================
🎯 二次点火: 【测试股票A】
   时间: 14:30:00
   排名: 5
   资金: 5.00亿
   周期: 30分钟
   评分: 9.50
   战术分: 18
   主线分: 15
   评级: ★★★★★ [绝对主线/立即开火!]
   理由: 命中绝对主线(人工智能/5家涨停) | 板块内已有小弟涨停 | 板块出现资金加速度警报
   解读: 市场最强合力所在，所有信号指向唯一目标，是执行纪律的时刻！
--------------------------------------------------------------------------------

================================================================================
```

## 技术实现细节

### 1. 代码修改位置

- **主函数**: 添加 `fire_signals_log_path` 参数
- **核心分析函数**: 增加绝对主线信号检测和输出逻辑
- **日志清理**: 包含新的日志文件模式
- **按小时分割**: 支持新的日志文件类型

### 2. 信号检测逻辑

```python
# 检测绝对主线/立即开火信号
fire_signals = [signal for signal in breakthrough_signals 
               if signal.get('评级', '').find('绝对主线/立即开火') != -1]
```

### 3. 日志写入逻辑

- 使用 `write_to_log_files()` 函数同时写入主日志和按小时分割日志
- 包含详细的错误处理机制
- 支持并发写入多个日志文件

## 配置参数

相关配置参数位于 `dynamic_gap_detector.py` 顶部：

```python
# 战术指令评级阈值
TACTICAL_THRESHOLD_FIRE = 15  # 15+分：绝对主线/立即开火

# 日志配置
SPLIT_LOG_BY_HOUR = True  # 是否按小时分割日志文件
```

## 注意事项

1. **文件权限**: 确保程序对 `log/` 目录有写入权限
2. **磁盘空间**: 长期运行可能产生大量日志文件，注意磁盘空间
3. **时间同步**: 确保系统时间准确，以保证信号时间戳的正确性
4. **编码格式**: 日志文件使用 UTF-8 编码，支持中文字符

## 维护建议

1. **定期清理**: 可以定期清理过期的日志文件
2. **监控文件大小**: 监控日志文件大小，避免单个文件过大
3. **备份重要信号**: 对于重要的"绝对主线/立即开火"信号，建议进行备份

## 总结

本次修改成功实现了"绝对主线/立即开火"信号的专门输出功能，确保：

- ✅ 所有符合条件的信号都能完整输出
- ✅ 按时间顺序记录，便于追踪
- ✅ 格式清晰，信息详细
- ✅ 支持按小时分割，便于管理
- ✅ 最小化修改，不影响原有功能

这个功能将帮助用户更好地捕捉和跟踪市场中最重要的投资机会。
