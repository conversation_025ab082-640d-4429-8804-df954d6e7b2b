#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向查询工具库 (V2.1 - 增加回测安全的历史最大值查询)
- 提供可供外部调用的函数，用于查询日线、分钟级、排行和统计数据。
- 所有函数均支持回测模式，通过 query_date 和 backtest_time 参数防止未来函数。
"""

import sqlite3
import os
import pandas as pd
from datetime import datetime

# --- 配置区域 (默认值，可在调用时被覆盖) ---
DB_PATH = r"D:\dev\mootdx\adata"
DB_PATH_MINUTE = r"D:\dev\mootdx\adata"
DB_NAME = "fund_flow.db"
DB_NAME_MINUTE = "fund_flow_minute.db"


def _get_db_connection(db_path, db_name, read_only=True):
    """通用数据库连接函数"""
    db_file = os.path.join(db_path, db_name)
    if not os.path.exists(db_file):
        raise FileNotFoundError(f"数据库文件不存在: {db_file}")
    try:
        # 在回测查询时，使用只读模式连接更安全
        if read_only:
            return sqlite3.connect(f'file:{db_file}?mode=ro', uri=True)
        else:
            return sqlite3.connect(db_file)
    except sqlite3.Error as e:
        raise Exception(f"连接数据库 {db_file} 失败: {e}")


def query_minute_inflow(stock_codes, query_date, backtest_time=None):
    """
    分钟流入查询：查询指定股票在指定日期的分钟级资金流入数据。

    参数:
    - stock_codes (list): 需要查询的股票代码列表。
    - query_date (str): 查询日期, 格式 'YYYY-MM-DD'。
    - backtest_time (str, optional): 回测截止时间, 格式 'HH:MM:SS'。
                                     如果提供，则只返回此时间点（含）之前的数据。

    返回:
    - pandas.DataFrame: 包含查询结果的DataFrame。
    """
    if not isinstance(stock_codes, list) or not stock_codes:
        return pd.DataFrame()

    with _get_db_connection(DB_PATH_MINUTE, DB_NAME_MINUTE) as conn:
        placeholders = ','.join('?' for _ in stock_codes)

        if backtest_time:
            query = f"""
            SELECT stock_code, time, main_net_inflow, super_large_net_inflow, large_net_inflow
            FROM minute_fund_flow
            WHERE date = ? AND stock_code IN ({placeholders}) AND time <= ?
            ORDER BY stock_code, time
            """
            params = [query_date] + stock_codes + [backtest_time]
        else:
            query = f"""
            SELECT stock_code, time, main_net_inflow, super_large_net_inflow, large_net_inflow
            FROM minute_fund_flow
            WHERE date = ? AND stock_code IN ({placeholders})
            ORDER BY stock_code, time
            """
            params = [query_date] + stock_codes

        return pd.read_sql_query(query, conn, params=params)


def query_daily_ranking(query_date, metric='main_net_inflow', top_n=20):
    """
    日度排行榜查询：查询指定日期特定指标的排行榜。

    参数:
    - query_date (str): 查询日期, 格式 'YYYY-MM-DD'。
    - metric (str): 需要查询的指标名称 (如 'main_net_inflow')。
    - top_n (int): 需要返回的排名前 N 的记录。

    返回:
    - pandas.DataFrame: 包含排行榜结果的DataFrame。
    """
    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        query = """
        SELECT rank_position, stock_code, stock_name, metric_value
        FROM daily_rankings
        WHERE date = ? AND metric = ?
        ORDER BY rank_position
        LIMIT ?
        """
        return pd.read_sql_query(query, conn, params=(query_date, metric, top_n))


def query_historical_high_ranking(query_date, metric='main_net_inflow', top_n=20):
    """
    查询指定日期创下历史新高的股票榜单。

    参数:
    - query_date (str): 查询日期, 格式 'YYYY-MM-DD'。
    - metric (str): 指标名称。
    - top_n (int): 返回的记录数。

    返回:
    - pandas.DataFrame: 包含创历史新高股票榜单的DataFrame。
    """
    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        query = """
        SELECT stock_code, current_value, historical_max_date
        FROM historical_max_rankings
        WHERE date = ? AND metric = ? AND is_historical_max = 1
        ORDER BY current_value DESC
        LIMIT ?
        """
        return pd.read_sql_query(query, conn, params=(query_date, metric, top_n))


def query_stock_stats(stock_codes, query_date):
    """
    查询个股的历史统计数据（回测安全）。
    自动查找不晚于 query_date 的最新统计数据。

    参数:
    - stock_codes (list): 股票代码列表。
    - query_date (str): 查询日期, 格式 'YYYY-MM-DD'。

    返回:
    - dict: 一个字典，键为统计类型 (如 'stats_up', 'extremes')，值为包含结果的DataFrame。
    """
    if not isinstance(stock_codes, list) or not stock_codes:
        return {}

    stats_results = {}
    stats_tables = {
        'stats_up': 'stock_stats_up',
        'stats_down': 'stock_stats_down',
        'extremes': 'stock_stats_extremes'
    }

    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        for key, table_name in stats_tables.items():
            all_dfs = []
            for code in stock_codes:
                # 关键：找到不晚于回测日期的最新统计日期
                date_query = f"""
                SELECT MAX(date) FROM {table_name}
                WHERE stock_code = ? AND date <= ?
                """
                cursor = conn.cursor()
                cursor.execute(date_query, (code, query_date))
                latest_stat_date = cursor.fetchone()[0]

                if latest_stat_date:
                    data_query = f"SELECT * FROM {table_name} WHERE stock_code = ? AND date = ?"
                    df = pd.read_sql_query(data_query, conn, params=(code, latest_stat_date))
                    all_dfs.append(df)

            if all_dfs:
                stats_results[key] = pd.concat(all_dfs, ignore_index=True)
            else:
                stats_results[key] = pd.DataFrame()

    return stats_results


def query_historical_max_value(stock_codes, query_date, metric='main_net_inflow'):
    """
    查询指定股票在某一日之前的历史最大资金流入值（回测安全）。

    此函数通过直接查询原始日线流水表，计算截止到 `query_date` 前一天（不含`query_date`）的历史最大值，
    确保在回测 `query_date` 当天时，使用的是纯粹的历史数据。

    参数:
    - stock_codes (list): 需要查询的股票代码列表。
    - query_date (str): 查询基准日期, 格式 'YYYY-MM-DD'。函数将查找此日期之前的最大值。
    - metric (str, optional): 需要查询的指标列名。默认为 'main_net_inflow'。

    返回:
    - pandas.DataFrame: 包含股票代码及其历史最大值的DataFrame，列为 ['stock_code', 'metric', 'historical_max_value']。
                       如果某股票无历史数据，其值为 None。
    """
    if not isinstance(stock_codes, list) or not stock_codes:
        return pd.DataFrame(columns=['stock_code', 'metric', 'historical_max_value'])

    # 指标白名单，防止SQL注入，确保安全
    allowed_metrics = [
        'main_net_inflow', 'main_net_ratio', 'super_large_net_inflow', 'super_large_net_ratio',
        'large_net_inflow', 'large_net_ratio', 'medium_net_inflow', 'medium_net_ratio',
        'small_net_inflow', 'small_net_ratio', 'close_price', 'change_pct'
    ]
    if metric not in allowed_metrics:
        raise ValueError(f"不安全的或不支持的指标: {metric}。请从白名单中选择。")

    results = []
    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        for code in stock_codes:
            # 核心查询逻辑: date < ? 确保不包含查询当天的数据
            query = f"""
            SELECT MAX({metric})
            FROM daily_fund_flow
            WHERE stock_code = ? AND date < ?
            """
            cursor = conn.cursor()
            cursor.execute(query, (code, query_date))
            max_value = cursor.fetchone()[0]
            results.append({
                'stock_code': code,
                'metric': metric,
                'historical_max_value': max_value
            })

    return pd.DataFrame(results)


def query_recent_days_max_value(stock_codes, query_date, metric='main_net_inflow', days=3):
    """
    查询指定股票在前N天内的最大资金流入值（回测安全）。

    此函数查询从 query_date 往前推 N 天的数据中的最大值，
    确保在回测时使用的是历史数据。

    参数:
    - stock_codes (list): 需要查询的股票代码列表。
    - query_date (str): 查询基准日期, 格式 'YYYY-MM-DD'。
    - metric (str, optional): 需要查询的指标列名。默认为 'main_net_inflow'。
    - days (int, optional): 查询前N天的数据，默认为3天。

    返回:
    - pandas.DataFrame: 包含股票代码及其前N天最大值的DataFrame，
                       列为 ['stock_code', 'metric', 'recent_max_value', 'days_checked']。
    """
    if not isinstance(stock_codes, list) or not stock_codes:
        return pd.DataFrame(columns=['stock_code', 'metric', 'recent_max_value', 'days_checked'])

    # 指标白名单，防止SQL注入，确保安全
    allowed_metrics = [
        'main_net_inflow', 'main_net_ratio', 'super_large_net_inflow', 'super_large_net_ratio',
        'large_net_inflow', 'large_net_ratio', 'medium_net_inflow', 'medium_net_ratio',
        'small_net_inflow', 'small_net_ratio', 'close_price', 'change_pct'
    ]
    if metric not in allowed_metrics:
        raise ValueError(f"不安全的或不支持的指标: {metric}。请从白名单中选择。")

    results = []
    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        for code in stock_codes:
            # 查询前N天的最大值，使用 date < query_date 确保不包含当天数据
            # 先获取前N个交易日，然后计算最大值
            cursor = conn.cursor()

            # 第一步：获取前N个交易日的数据
            query_dates = f"""
            SELECT date, {metric}
            FROM daily_fund_flow
            WHERE stock_code = ?
            AND date < ?
            ORDER BY date DESC
            LIMIT ?
            """
            cursor.execute(query_dates, (code, query_date, days))
            recent_data = cursor.fetchall()

            if recent_data:
                # 计算最大值
                values = [row[1] for row in recent_data if row[1] is not None]
                max_value = max(values) if values else None
                actual_days = len(recent_data)
            else:
                max_value = None
                actual_days = 0

            results.append({
                'stock_code': code,
                'metric': metric,
                'recent_max_value': max_value,
                'days_checked': actual_days
            })

    return pd.DataFrame(results)


def find_exceeded_days_count(stock_codes, query_date, current_values, metric='main_net_inflow', max_days=100):
    """
    查找当前值超过前N天最大值的具体天数（回测安全）。

    此函数逐步检查前1天、前2天、...、前N天的最大值，
    找出当前值能超过的最大天数。

    参数:
    - stock_codes (list): 需要查询的股票代码列表。
    - query_date (str): 查询基准日期, 格式 'YYYY-MM-DD'。
    - current_values (dict): 当前值字典，格式 {stock_code: current_value}。
    - metric (str, optional): 需要查询的指标列名。默认为 'main_net_inflow'。
    - max_days (int, optional): 最大检查天数，默认为100天。

    返回:
    - pandas.DataFrame: 包含股票代码及其超过的天数的DataFrame，
                       列为 ['stock_code', 'metric', 'exceeded_days', 'max_days_value']。
    """
    if not isinstance(stock_codes, list) or not stock_codes:
        return pd.DataFrame(columns=['stock_code', 'metric', 'exceeded_days', 'max_days_value'])

    # 指标白名单，防止SQL注入，确保安全
    allowed_metrics = [
        'main_net_inflow', 'main_net_ratio', 'super_large_net_inflow', 'super_large_net_ratio',
        'large_net_inflow', 'large_net_ratio', 'medium_net_inflow', 'medium_net_ratio',
        'small_net_inflow', 'small_net_ratio', 'close_price', 'change_pct'
    ]
    if metric not in allowed_metrics:
        raise ValueError(f"不安全的或不支持的指标: {metric}。请从白名单中选择。")

    results = []
    with _get_db_connection(DB_PATH, DB_NAME) as conn:
        for code in stock_codes:
            current_value = current_values.get(code)
            if current_value is None or pd.isna(current_value):
                results.append({
                    'stock_code': code,
                    'metric': metric,
                    'exceeded_days': 0,
                    'max_days_value': None
                })
                continue

            cursor = conn.cursor()
            exceeded_days = 0
            max_days_value = None

            # 逐步检查前1天、前2天、...、前max_days天
            for days in range(1, max_days + 1):
                # 获取前N个交易日的数据
                query_dates = f"""
                SELECT {metric}
                FROM daily_fund_flow
                WHERE stock_code = ?
                AND date < ?
                ORDER BY date DESC
                LIMIT ?
                """
                cursor.execute(query_dates, (code, query_date, days))
                recent_data = cursor.fetchall()

                if recent_data and len(recent_data) >= days:
                    # 计算前N天的最大值
                    values = [row[0] for row in recent_data if row[0] is not None]
                    if values:
                        max_value = max(values)
                        if current_value > max_value:
                            exceeded_days = days
                            max_days_value = max_value
                        else:
                            # 如果当前天数不能超过，后续更多天数也不可能超过
                            break
                else:
                    # 数据不足，停止检查
                    break

            results.append({
                'stock_code': code,
                'metric': metric,
                'exceeded_days': exceeded_days,
                'max_days_value': max_days_value
            })

    return pd.DataFrame(results)


# --- 主程序块，用于直接运行此文件进行测试 ---
if __name__ == "__main__":
    TEST_STOCKS = ['600570', '002230']  # 恒瑞医药, 科大讯飞
    TEST_DATE = "2025-07-28"

    print(f"--- 资金流向查询工具库测试 (日期: {TEST_DATE}) ---")

    # 1. 测试分钟数据 (回测模式)
    print("\n[1] 测试分钟数据查询 (回测模式 @ 09:45:00)...")
    minute_data = query_minute_inflow(TEST_STOCKS, TEST_DATE, backtest_time="09:45:00")
    print(f"查询到 {len(minute_data)} 条记录。")
    print(minute_data.tail())

    # 2. 测试日度排行榜
    print("\n[2] 测试日度排行榜查询 (主力净流入)...")
    ranking_data = query_daily_ranking(TEST_DATE, metric='main_net_inflow', top_n=5)
    print(ranking_data)

    # 3. 测试创历史新高榜
    print("\n[3] 测试创历史新高榜查询...")
    high_data = query_historical_high_ranking(TEST_DATE, metric='main_net_inflow', top_n=5)
    print(high_data)

    # 4. 测试历史统计数据
    print(f"\n[4] 测试个股历史统计查询 (截至 {TEST_DATE})...")
    stats = query_stock_stats(TEST_STOCKS, TEST_DATE)
    for name, df in stats.items():
        print(f"\n--- 统计类型: {name} ---")
        if not df.empty:
            print(df.to_string())
        else:
            print("无数据")

    # 5. 【新增】测试历史最大值查询
    print(f"\n[5] 测试历史最大值查询 (查询 {TEST_DATE} 之前的最大主力净流入)...")
    hist_max_data = query_historical_max_value(TEST_STOCKS, TEST_DATE, metric='main_net_inflow')
    if not hist_max_data.empty:
        # 格式化输出方便查看
        hist_max_data['historical_max_value'] = hist_max_data['historical_max_value'].apply(
            lambda x: f"{x / 1e8:.2f}亿" if pd.notna(x) else "无历史数据"
        )
        print(hist_max_data.to_string(index=False))
    else:
        print("未查询到历史最大值数据。")