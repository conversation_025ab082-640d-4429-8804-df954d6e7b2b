# 历史突破信号检测器 (HistoricalBreakthroughDetector)

## 功能概述

历史突破信号检测器是一个全新的信号检测模块，专门用于捕捉基于"大于历史资金流入"指标变化的买入信号。该模块通过状态追踪机制，对比前后两个时间点的股票状态，识别出两种关键的突破信号。

## 信号类型

### 1. 二次点火 (Top-Tier Ignition)
**类型系数**: 1.0

**触发条件**:
- 上一个时间点：股票排名在前10名，但没有"大于历史资金流入"标记
- 当前时间点：股票排名仍在前10名，首次出现"大于历史资金流入"标记

**含义**: 已经在前排的强势股票，突破了历史资金流入记录，显示出更强的爆发力。

### 2. 横空出世 (Breakout Emergence)
**类型系数**: 1.2

**触发条件**:
- 上一个时间点：股票排名在50名以外或不在榜单上
- 当前时间点：首次冲入前50名，且带有"大于历史资金流入"标记

**含义**: 之前不起眼的股票突然爆发，以历史性的资金流入强势冲入前排。

### 3. 突破强化 (Breakthrough Reinforcement) 🆕
**类型系数**: 1.1

**触发条件** (必须同时满足):
- 上一个时间点：股票排名在前10名，且已有"大于历史资金流入"标记
- 当前时间点：股票排名仍在前10名，且仍有"大于历史资金流入"标记
- 历史突破天数增长：当前天数 > 上一时间点天数
- 资金流入增长：当前主力净流入 > 上一时间点 × 1.1 (至少增长10%)

**含义**: 已经突破历史的强势股票继续强化，突破更长的历史周期并伴随资金流入的显著增长，显示持续的强劲动能。

## 评分算法

### 综合分数计算公式
```
综合分数 = math.log(主力净流入金额 / 10000) * (1 + 历史天数 / 100) * 类型系数
```

### 历史天数解析规则
- `'前XX天'` → 提取XX作为历史天数
- `'是'` → 分配101天（表示突破了非常长期的记录）
- 空值或无效文本 → 0天

### 评分特点
- **资金规模影响**: 使用对数函数，避免极大值主导
- **历史意义加权**: 突破的历史周期越长，权重越高
- **信号类型区分**: 三种信号类型系数：二次点火(1.0)、突破强化(1.1)、横空出世(1.2)

## 技术实现

### 状态追踪机制 🆕扩展
- 使用 `self.previous_stock_states` 字典存储上一个时间点的股票状态
- 扩展的状态结构包含：
  - `rank`: 排名
  - `gthi_has_signal`: 是否有历史标记 (布尔值)
  - `gthi_days`: 历史突破天数 (整数)
  - `net_inflow`: 主力净流入金额 (浮点数)
- 确保回测安全，不使用未来数据
- 兼容旧状态格式的自动转换

### 集成方式
1. 在 `_run_analysis_core()` 函数中实例化检测器（循环外）
2. 在个股资金流数据处理完成后调用 `detect_signals()` 方法
3. 使用 tabulate 库格式化输出信号表格

### 输出格式
检测到信号时会显示：
```
🚀 历史突破信号检测 (模拟时间点: HH:MM:SS) 🚀
┌──────────┬────────────┬──────────┬──────────────┬──────────┬──────────┐
│ 信号类型 │ 股票名称   │ 当前排名 │ 主力净流入   │ 突破周期 │ 综合评分 │
├──────────┼────────────┼──────────┼──────────────┼──────────┼──────────┤
│ 二次点火 │ 股票A      │        1 │ 9000.00万    │ 前10天   │    10.02 │
│ 横空出世 │ 股票E      │        4 │ 5500.00万    │ 前20天   │    12.40 │
└──────────┴────────────┴──────────┴──────────────┴──────────┴──────────┘
```

## 使用场景

### 适用情况
- 盘中实时监控强势股票的历史突破
- 识别资金流入的关键转折点
- 捕捉潜在的爆发性机会

### 注意事项
- 需要配合"大于历史资金流入"功能使用
- 信号质量依赖于历史数据的完整性
- 建议结合其他技术指标综合判断

## 测试验证

模块包含完整的单元测试(`test_breakthrough_detector.py`)，验证：
- 文本解析功能的准确性
- 评分算法的正确性  
- 信号检测逻辑的完整性
- 边界情况的处理

运行测试：
```bash
python test_breakthrough_detector.py
```

## 更新日志

**v1.0** (2025-07-29)
- 初始版本发布
- 实现两种信号类型检测
- 集成到主分析流程
- 完成测试验证

**v1.1** (2025-07-29) 🆕
- 新增"突破强化"信号类型检测
- 扩展状态追踪机制，增加历史天数和资金流入记录
- 更新评分算法支持三种信号类型
- 完成扩展功能测试验证
