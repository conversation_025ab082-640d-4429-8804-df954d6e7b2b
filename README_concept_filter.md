# 概念和板块过滤器模块

## 概述

`concept_sector_filter.py` 是从 `dynamic_gap_detector.py` 中分离出来的独立模块，专门用于过滤无意义的概念和板块名称。该模块可以被其他文件调用，提供统一的概念过滤功能。

## 功能特点

- **独立模块**: 可以被任何Python文件导入和使用
- **多种数据类型支持**: 支持列表、字典等不同数据结构
- **完整的过滤规则**: 包含44个无意义概念和板块的过滤规则
- **向后兼容**: 保持与原有代码的兼容性
- **易于扩展**: 可以轻松添加新的过滤规则

## 主要函数

### 1. `filter_meaningful_concepts_and_sectors(concepts_or_sectors)`
主要的过滤函数，支持列表和字典两种数据类型。

```python
from concept_sector_filter import filter_meaningful_concepts_and_sectors

# 过滤列表
concepts = ['人工智能', '昨日涨停', '芯片概念', '融资融券']
filtered = filter_meaningful_concepts_and_sectors(concepts)
# 结果: ['人工智能', '芯片概念']

# 过滤字典
concept_stats = {'人工智能': 15, '昨日涨停': 8, '芯片概念': 12}
filtered_stats = filter_meaningful_concepts_and_sectors(concept_stats)
# 结果: {'人工智能': 15, '芯片概念': 12}
```

### 2. `is_meaningful_concept(concept_name)`
判断单个概念是否有意义。

```python
from concept_sector_filter import is_meaningful_concept

print(is_meaningful_concept('人工智能'))  # True
print(is_meaningful_concept('昨日涨停'))  # False
```

### 3. `filter_concept_list(concept_list)`
专门用于过滤概念列表。

```python
from concept_sector_filter import filter_concept_list

concepts = ['软件开发', '沪股通', '半导体', '深股通']
filtered = filter_concept_list(concepts)
# 结果: ['软件开发', '半导体']
```

### 4. `filter_concept_dict(concept_dict)`
专门用于过滤概念字典。

```python
from concept_sector_filter import filter_concept_dict

concept_stats = {'AI概念': 10, 'MSCI': 5, '新能源': 8}
filtered = filter_concept_dict(concept_stats)
# 结果: {'AI概念': 10, '新能源': 8}
```

### 5. `get_meaningless_items()`
获取所有无意义概念的集合。

```python
from concept_sector_filter import get_meaningless_items

meaningless_set = get_meaningless_items()
print(f"无意义概念总数: {len(meaningless_set)}")
```

## 过滤的无意义概念

模块会过滤掉以下类型的概念和板块：

- **时间相关**: 昨日涨停、昨日连板等
- **指数相关**: 上证50、中证500、MSCI等
- **资金相关**: 融资融券、基金重仓、机构重仓等
- **特殊标记**: ST板块、微盘股、低价股等
- **通道相关**: 沪股通、深股通、港股通等

完整列表包含44个无意义概念，详见代码中的 `meaningless_items` 集合。

## 在其他文件中使用

### 基本导入和使用

```python
# 导入主要函数
from concept_sector_filter import filter_meaningful_concepts_and_sectors

# 在你的代码中使用
def process_stock_concepts(stock_concepts):
    """处理股票概念数据"""
    filtered_concepts = filter_meaningful_concepts_and_sectors(stock_concepts)
    return filtered_concepts
```

### 替换原有的过滤逻辑

如果你的代码中已经有类似的过滤逻辑，可以直接替换：

```python
# 原来的代码
# filtered_concepts = [c for c in concepts if c not in meaningless_items]

# 新的代码
from concept_sector_filter import filter_meaningful_concepts_and_sectors
filtered_concepts = filter_meaningful_concepts_and_sectors(concepts)
```

## 测试和验证

运行测试示例：

```bash
# 测试过滤器模块本身
python concept_sector_filter.py

# 运行完整的使用示例
python example_usage.py
```

## 文件结构

```
├── concept_sector_filter.py    # 主要的过滤器模块
├── example_usage.py           # 使用示例
├── dynamic_gap_detector.py    # 已更新为使用新的过滤器
└── README_concept_filter.md   # 本说明文件
```

## 更新说明

- **从 dynamic_gap_detector.py 分离**: 原有的 `filter_meaningful_concepts_and_sectors` 函数已从 `dynamic_gap_detector.py` 中移除，改为导入此模块
- **功能增强**: 新增了多个便利函数，提供更灵活的使用方式
- **向后兼容**: 保持了原有函数的接口和行为不变

## 扩展和维护

如需添加新的无意义概念，请修改 `get_meaningless_items()` 函数中的集合。建议同时更新测试用例以确保新规则正常工作。
