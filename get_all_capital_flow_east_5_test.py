import akshare as ak
import pandas as pd
import schedule
import time as time_module
import requests
from datetime import datetime, time
from tabulate import tabulate
from dotenv import load_dotenv
import os
import numpy as np
import logging
import struct
import urllib.parse
import sqlite3
import threading
import warnings
import re
import tempfile # --- 新增：用于创建安全的临时文件
from sklearn.linear_model import LinearRegression # 确保引入
import pickle  # --- 新增：用于缓存序列化
import json    # --- 新增：用于缓存序列化

# 忽略pandas的FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning, module="pandas")
# 忽略akshare库中的FutureWarning
warnings.filterwarnings("ignore", category=FutureWarning, module="akshare")
df_analysis_global = pd.DataFrame()

# --- 新增：股票名称缓存，避免重复API调用 ---
STOCK_NAME_CACHE = {}
CACHE_DATE = None

# --- 新增：板块映射缓存配置 ---
ENABLE_BOARD_CACHE = True  # 是否启用板块映射缓存
UPDATE_BOARD_CACHE = False  # 是否强制更新缓存，默认为否
BOARD_CACHE_EXPIRE_DAYS = 0  # 缓存过期天数，0表示永不过期
BOARD_CACHE_DIR = "cache"  # 缓存目录

# DEBUG模式开关，默认为False
DEBUG_MODE = True
# 历史数据打印开关，默认为False
PRINT_HISTORY = False
# 交易时间开关，默认为True。如果设为  False，则忽略交易时间限制
ENABLE_TRADING_TIME_CHECK = False
# 生效的买入条件编号，例如[1, 2, 3, 4, 5, 6, 7, 11]，为空则不买入
ACTIVE_BUY_CONDITIONS = [] #2, 5, 6, 7
# 生效的卖出条件编号，例如[1, 2, 3, 4, 5, 6, 7, 8, 12]，为空则不卖出
ACTIVE_SELL_CONDITIONS = [1, 2, 3, 4, 5, 6, 7, 8]

SCAN_STOCK_TYPES = ['60', '00', '30', '688']
MIN_MAIN_NET_INFLOW = 50000000  # 今日主力净流入最小金额（例如5000万），买入信号需满足此条件

# --- 新增：文件写入锁，防止多任务冲突 ---
# --- 新增开始：文件写入锁，防止多任务冲突 ---
file_lock = threading.Lock()
is_positions_scan_running = False
is_market_scan_running = False


# --- 新增：用于记录龙头信号首次出现时间的缓存 ---
FIRST_SIGNAL_TIMES = {}

PREVIOUS_SECTOR_DATA = {} # 存储: {'板块名称': {'inflow': value, 'rank': value}}


INTENSITY_LEVEL_1_RANK = 40
INTENSITY_LEVEL_2_RANK = 15

# 异动拉升 (Breakouts)
BREAKOUT_INITIAL_AVG_RANK = 40
BREAKOUT_FINAL_RANK = 20
BREAKOUT_RANK_TREND_MIN_SLOPE = -0.5
BREAKOUT_INFLOW_TREND_MIN_SLOPE = 1e6

# 下午盘二次加速配置
PM_ACCELERATION_FACTOR = 2.0
PM_ACCELERATION_MIN_SLOPE = 2e6

# 时间区间定义
AM_END_TIME = time(11, 30)
PM_START_TIME = time(13, 0)


# --- 【V5.2 新增】主线识别再增强：持续性强度判断 ---
SECTOR_STRENGTH_TRACKER = {} # 格式: {'板块名称': 连续被识别为强势板块的次数}
MAINLINE_CONTINUITY_THRESHOLD = 30 # 连续30分钟被识别为强势板块，即可确认为“持续性主线”
# --- 【新增】板块聚合分析与评分制相关全局变量 ---
# 用于存储当前最强的板块列表，由新函数 get_strong_sectors 动态更新
STRONG_SECTORS_LIST = []
# 用于存储当前异动拉升的股票列表，由 run_breakout_scan 动态更新
BREAKOUT_STOCKS_LIST = []
BUY_SCORE_THRESHOLD = 7  # 总分达到7分或以上，触发买入信号

# --- 【V5.0 核心升级】主线与混战模式 ---
# --- 【V5.0 核心升级】主线与混战模式 ---
MAINLINE_SECTOR_LIST = [] # 用于存储当前已确认的“主线”板块，由 task_analyze_strong_sectors 动态更新
BUY_SCORE_THRESHOLD_NON_MAINLINE = 10
# --- V6.0 核心升级：多因子评分阈值 ---
MAINLINE_SCORE_THRESHOLD = 8    # 主线总分阈值
POTENTIAL_SCORE_THRESHOLD = 5   # 潜在支线总分阈值



# --- V6.0 核心升级：多因子评分阈值 ---


# --- 【V2.4 新增】强势板块动态评分参数 ---
SECTOR_SCORE_THRESHOLD = 5         # 板块综合得分达到此阈值才被视为强势板块
LIMIT_UP_COUNT_MIN = 1             # 板块中至少要有1个涨停股，才参与高分计算
SECTOR_GAP_RATIO_THRESHOLD = 1.5   # 头部断层效应：第一名/第二名资金比率阈值
SECTOR_TOP_TIER_PERCENTILE = 0.95  # 顶级资金百分位线 (排名前5%)
SECTOR_SECOND_TIER_PERCENTILE = 0.85 # 次顶级资金百分位线 (排名前15%)


# 定义每个买入条件的得分
# 【V4.0 重大升级 & 交易大师V2优化】定义每个买入条件的得分
BUY_CONDITIONS_SCORE = {
    # 环境分
    'mainline_sector_bonus': 10,  # 主线板块加成 (龙头中的龙头) - 权重提高
    'sector_bonus': 3,          # 普通强势板块加成
    'breakout_bonus': 3,        # 异动拉升
    'market_rank_top_3': 5,     # 【新增】市场总龙头核心分

    # 板块内地位分
    'sector_rank_1': 5,         # 板块内资金排名第一
    'sector_rank_top_3': 3,     # 板块内资金排名前三

    # 个股技术形态分
    'fund_advantage': 2,        # 资金优势 (原条件2)
    'chip_concentration': 2,    # 筹码集中 (原条件5)
    'history_anomaly': 2,       # 历史异动 (原条件7)

    # 价格行为分
    'limit_up_bonus': 4,        # 【新增】涨停或高位（>9.8%）给予重奖
    'price_breakout_strong': 3, # 涨幅 > 7%
    'price_breakout_medium': 2, # 涨幅 > 5%
    'price_breakout_weak': 1,   # 涨幅 > 3%
}
# --- 排名异动信号设计新的推送模板 ---
RANK_CHANGE_TEMPLATE = (
    "🚀 排名异动: {stock_name} ({stock_code})\n"
    "股票类型: {stock_type}\n"
    "价格: {price:.2f} | 涨跌幅: {change:.2f}%\n"
    "排名变化: 从 {old_rank} 名跃升至 {new_rank} 名!\n"
    "主力净占比: 从 {old_ratio:.2f}% 飙升至 {new_ratio:.2f}%!\n"
    "主力净流入: {main_flow}\n"
    "时间: {time}"
)


# 【新增】将此新函数添加到 generate_master_analysis_report 函数之前
# 【新增】将此新函数添加到 generate_master_analysis_report 函数之前
def _dynamic_theme_discovery(df_analysis, top_n=10, min_freq=2):
    """
    【交易大师V3.2 · 动态版】从Top N板块中动态发现主题。
    :param df_analysis: 包含板块评分的DataFrame
    :param top_n: 分析排名前N的板块
    :param min_freq: 一个词根至少出现几次才被认为是主题的一部分
    :return: 一个包含主题词和其总分的元组, e.g., (['AI', '智能'], 150)
    """
    if df_analysis.empty or len(df_analysis) < min_freq:
        return None, 0

    top_sectors = df_analysis.head(top_n)

    # 1. 提取所有板块名称中的潜在词根 (2个或3个字符的词)
    word_scores = {}
    word_counts = {}

    # 定义一些需要排除的通用词或无意义的词
    stop_words = {'概念', '行业', '服务', '开发', '研究', '技术', '应用', '设备', '制造', '材料'}

    for _, row in top_sectors.iterrows():
        name = row['板块名称']
        score = row['总分']

        # 提取所有长度为2和3的子字符串作为候选词根
        words = set()
        for i in range(len(name) - 1):
            words.add(name[i:i + 2])
        if len(name) > 2:
            for i in range(len(name) - 2):
                words.add(name[i:i + 3])

        # 累加分数和计数
        for word in words:
            if word in stop_words or word.isdigit():
                continue
            word_scores[word] = word_scores.get(word, 0) + score
            word_counts[word] = word_counts.get(word, 0) + 1

    if not word_scores:
        return None, 0

    # 2. 找到频率足够高、总分足够高的核心主题词
    candidate_themes = []
    for word, count in word_counts.items():
        if count >= min_freq:
            candidate_themes.append((word, word_scores[word]))

    if not candidate_themes:
        return None, 0

    # 按总分排序，选出最强的词根作为主题核心
    candidate_themes.sort(key=lambda x: x[1], reverse=True)

    # 3. 构建主题：将相关的、高分的词根聚合在一起
    main_theme_word, main_theme_score = candidate_themes[0]
    final_theme_words = [main_theme_word]
    final_theme_total_score = main_theme_score

    # 聚合其他与主词根相关的词
    for word, score in candidate_themes[1:]:
        # 如果一个词是主词的子串，或者主词是它的子串，就认为它们相关
        if word in main_theme_word or main_theme_word in word:
            if word not in final_theme_words:
                final_theme_words.append(word)

    # 返回聚合后的主题词列表和主词根的分数
    return final_theme_words, main_theme_score


# 【最终替换版】将此函数完整地复制并替换掉 get_all_capital_flow_east.py 和 backtestv5_test.py 中的旧版本
# 【最终替换版】将此函数完整地复制并替换掉 get_all_capital_flow_east.py 和 backtestv5_test.py 中的旧版本
def generate_master_analysis_report(df_analysis, mainline_threshold, potential_threshold):
    """【V8.6 升级版】生成包含板块状态的市场主报告"""
    if df_analysis.empty:
        return "市场主线模糊，资金攻击方向不明。"

    report_lines = []
    
    # 主线板块
    mainline_df = df_analysis[df_analysis['总分'] >= mainline_threshold]
    if not mainline_df.empty:
        report_lines.append("**主线板块:**")
        for _, row in mainline_df.iterrows():
            # --- 【修改】增加状态显示，安全处理可能缺失的状态列 ---
            status_tag = f" ({row['状态']})" if '状态' in row and row['状态'] else ""
            report_lines.append(f"- {row['板块名称']}{status_tag}")
    else:
        report_lines.append("**主线板块:** 主线模糊")

    # 强势/潜力板块
    potential_df = df_analysis[(df_analysis['总分'] >= potential_threshold) & (df_analysis['总分'] < mainline_threshold)]
    if not potential_df.empty:
        report_lines.append("\n**强势/潜力板块:**")
        for _, row in potential_df.iterrows():
            # --- 【修改】增加状态显示，安全处理可能缺失的状态列 ---
            status_tag = f" ({row['状态']})" if '状态' in row and row['状态'] else ""
            report_lines.append(f"- {row['板块名称']}{status_tag}")

    return '\n'.join(report_lines)

def task_generate_daily_buy_summary():
    """
    (独立任务) 每日收盘后，读取当天的信号记录文件，生成一份买入信号的汇总报告。
    该任务应在所有盘中扫描任务结束后执行。
    """
    # 即使在非交易日被意外触发，也跳过执行
    if not is_trading_day():
        logging.info("非交易日，跳过生成每日买入汇总报告。")
        return

    logging.info("--- [任务开始] 生成每日买入信号汇总报告 ---")
    print("\n--- [任务开始] 生成每日买入信号汇总报告 ---")

    try:
        # 获取当天的文件夹和文件路径
        date_folder = get_date_folder()
        source_file = os.path.join(date_folder, 'stock_signals.csv')
        summary_file_path = os.path.join(date_folder, f'daily_buy_summary_{datetime.now().strftime("%Y%m%d")}.txt')

        # 检查源文件是否存在
        if not os.path.exists(source_file):
            msg = f"信号记录文件 {source_file} 不存在，今日无信号，无法生成汇总报告。"
            logging.warning(msg)
            print(msg)
            return

        # 读取所有信号记录
        all_signals_df = pd.read_csv(source_file, encoding='utf-8-sig')

        # 筛选出买入信号
        buy_signals_df = all_signals_df[all_signals_df['Signal'] == 'Buy'].copy()

        if buy_signals_df.empty:
            msg = "今日无买入信号记录，不生成汇总报告。"
            logging.info(msg)
            print(msg)
            return

        # 按时间升序排序
        buy_signals_df.sort_values(by='Time', ascending=True, inplace=True)

        # 选择并格式化用于报告的列
        report_df = buy_signals_df[[
            'Time', 'Stock_Code', 'Stock_Name', 'Price', 'Change_Percent', 'Main_Flow'
        ]].copy()

        # 将 Main_Flow（单位：万）转换回原始金额以供 format_amount 函数正确处理
        report_df['Main_Flow_Formatted'] = report_df['Main_Flow'].apply(lambda x: format_amount(x * 10000))
        report_df['Price'] = report_df['Price'].apply(lambda x: f"{x:.2f}")
        report_df['Change_Percent'] = report_df['Change_Percent'].apply(lambda x: f"{x:.2f}%")

        # 准备表格化输出
        table_headers = ['触发时间', '股票代码', '股票名称', '当时价格', '当时涨幅', '主力净流入']
        table_content = report_df[
            ['Time', 'Stock_Code', 'Stock_Name', 'Price', 'Change_Percent', 'Main_Flow_Formatted']]

        report_table = tabulate(
            table_content,
            headers=table_headers,
            tablefmt='psql',
            showindex=False
        )

        # 准备完整的报告文本
        today_str = datetime.now().strftime('%Y-%m-%d')
        report_title = f"====== {today_str} 买入信号汇总报告 ======\n\n"
        final_report_text = report_title + report_table

        # 写入汇总文件
        with open(summary_file_path, 'w', encoding='utf-8') as f:
            f.write(final_report_text)

        success_msg = f"成功生成每日买入汇总报告: {summary_file_path}"
        logging.info(success_msg)
        print(success_msg)
        print(final_report_text)  # 同时在控制台打印报告

    except FileNotFoundError:
        msg = f"信号记录文件 {source_file} 未找到，可能今日未产生任何信号。"
        logging.warning(msg)
        print(msg)
    except Exception as e:
        error_msg = f"生成每日买入汇总报告时发生错误: {e}"
        logging.error(error_msg)
        import traceback
        logging.error(traceback.format_exc())
        print(error_msg)
    finally:
        logging.info("--- [任务结束] 每日买入信号汇总完成 ---")


def write_signal_file_atomically(file_path, new_codes, overwrite=False):
    """
    (线程安全且进程安全) 使用"写入临时文件再替换"的原子操作模式创建或更新信号文件。
    这可以彻底避免iQuant端读取到0字节或不完整文件的问题。

    :param file_path: 目标文件路径, e.g., "D:/flow_buy.ebk"
    :param new_codes: 本次要写入的新股票代码列表
    :param overwrite: 是否覆盖文件。True=覆盖(用于买入)，False=合并(用于卖出)
    """
    if not new_codes:
        logging.debug(f"无新信号代码需要写入 {file_path}，跳过文件操作。")
        # 如果是覆盖模式且无信号，则删除旧文件，确保iQuant不会读到过期信号
        if overwrite and os.path.exists(file_path):
            try:
                with file_lock:
                    os.remove(file_path)
                    logging.info(f"无买入信号，已删除旧的信号文件: {file_path}")
                    print(f"无买入信号，已删除旧的信号文件: {file_path}")
            except Exception as e:
                logging.error(f"删除旧信号文件 {file_path} 失败: {e}")
        return

    with file_lock:
        try:
            final_codes = set(new_codes)
            # 合并模式：如果不是覆盖，则读取旧文件中的代码并合并
            if not overwrite and os.path.exists(file_path):
                try:
                    # 使用GBK编码读取，以防万一旧文件是GBK格式
                    with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                        existing_codes = {line.strip() for line in f if line.strip()}
                        final_codes.update(existing_codes)
                except Exception as e:
                    logging.warning(f"读取旧信号文件 {file_path} 以进行合并时失败: {e}。将只写入新信号。")

            sorted_codes = sorted(list(final_codes))

            # 创建一个与目标文件在同一目录的临时文件
            temp_file_path = file_path + ".tmp"

            # 写入临时文件
            with open(temp_file_path, 'w', encoding='gbk') as f:  # 确保写入iQuant能识别的GBK编码
                for code in sorted_codes:
                    # 保持原有的换行+代码格式
                    f.write(f"\n{code}")

            # 【核心修复】使用os.replace()替代os.rename()，以原子性地覆盖Windows上已存在的文件
            os.replace(temp_file_path, file_path)

            action = "覆盖生成" if overwrite else "更新合并"
            logging.info(f"成功{action}信号文件: {file_path}，包含 {len(sorted_codes)} 条代码。")
            print(f"成功{action}信号文件: {file_path}，包含 {len(sorted_codes)} 条代码。")

        except Exception as e:
            logging.error(f"原子写入信号文件 {file_path} 失败: {e}")
            print(f"原子写入信号文件 {file_path} 失败: {e}")
            # 如果替换失败，尝试清理临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)


def get_date_folder():
    """获取按日期命名的文件夹路径（如 data/20250618）"""
    date_str = datetime.now().strftime('%Y%m%d')
    folder = os.path.join('data', date_str)
    os.makedirs(folder, exist_ok=True)
    return folder


date_folder = get_date_folder()
log_file = os.path.join(date_folder, 'stock_monitor.log')
logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
# 添加控制台输出处理器
# console_handler = logging.StreamHandler()
# console_handler.setLevel(logging.DEBUG)
# console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
# logging.getLogger('').addHandler(console_handler)

# 获取脚本所在目录的父目录（项目根目录）
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
env_path = os.path.join(project_root, '.env')
load_dotenv(env_path)

BARK_URL = os.getenv('BARK_URL')
ENABLE_BUY_NOTIFICATION = os.getenv('ENABLE_BUY_NOTIFICATION', 'True').lower() == 'true'
ENABLE_SELL_NOTIFICATION = os.getenv('ENABLE_SELL_NOTIFICATION', 'True').lower() == 'true'
if not BARK_URL:
    raise ValueError("BARK_URL not found in .env file")

pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

# --- 新增开始：全局推送缓存 ---
# 用于在内存中记录当天的推送历史，避免频繁读写文件
# 键格式: (stock_code, signal_type)，值: {main_ratio: float}
TODAY_NOTIFICATION_CACHE = {}

# --- 新增：排名异动监控全局变量 ---
# 用于存储上一次扫描的股票排名数据，以便进行排名异动比较
# 键格式: stock_code，值: {'rank': int, 'main_ratio': float, 'full_row': row_data}
PREVIOUS_RANK_DATA = {}
PREVIOUS_INFLOW_DATA = {}

# --- 新增结束 ---
# --- 新增：资金加速度监控配置 ---
ACCELERATION_THRESHOLD_AMOUNT = 50000000  # 单分钟资金流入增量阈值 (1亿)
ACCELERATION_PUSH_COOLDOWN = 60 * 10       # 相同股票的加速度信号推送冷却时间（秒），这里是10分钟


def format_amount(amount):
    """将金额转换为友好格式（如 7000万、1亿）"""
    amount = float(amount)
    if abs(amount) >= 100000000:
        return f"{amount / 100000000:.2f}亿"
    elif abs(amount) >= 10000:
        return f"{amount / 10000:.2f}万"
    return f"{amount:.2f}"


BUY_TEMPLATE = (
    "📈 买入信号: {stock_name} ({stock_code})\n"
    "股票类型: {stock_type}\n"
    "价格: {price:.2f} | 涨跌幅: {change:.2f}%\n"
    "主力净流入: {main_flow} ({main_ratio:.2f}%)\n"
    "超大单: {super_flow} ({super_ratio:.2f}%)\n"
    "大单: {big_flow} ({big_ratio:.2f}%)\n"
    "中单: {mid_flow} ({mid_ratio:.2f}%)\n"
    "小单: {small_flow} ({small_ratio:.2f}%)\n"
    "时间: {time}"
)

SELL_TEMPLATE = (
    "📉 卖出信号: {stock_name} ({stock_code})\n"
    "股票类型: {stock_type}\n"
    "价格: {price:.2f} | 涨跌幅: {change:.2f}%\n"
    "主力净流入: {main_flow} ({main_ratio:.2f}%)\n"
    "超大单: {super_flow} ({super_ratio:.2f}%)\n"
    "大单: {big_flow} ({big_ratio:.2f}%)\n"
    "中单: {mid_flow} ({mid_ratio:.2f}%)\n"
    "小单: {small_flow} ({small_ratio:.2f}%)\n"
    "触发条件: {hit_conditions}\n"
    "时间: {time}"
)

BIG_DEAL_TEMPLATE = (
    "📊 大单追踪: {stock_name} ({stock_code})\n"
    "股票类型: {stock_type}\n"
    "成交时间: {deal_time}\n"
    "成交价格: {price:.2f} | 成交量: {volume}股 | 成交额: {amount:.2f}万元\n"
    "大单性质: {deal_type}\n"
    "涨跌幅: {change:.2f}% | 涨跌额: {change_amount}\n"
    "时间: {time}"
)

date_folder = get_date_folder()
RECORD_FILE = os.path.join(date_folder, 'stock_signals.csv')
NOTIFICATION_FILE = os.path.join(date_folder, 'stock_notifications.csv')
if not os.path.exists(RECORD_FILE):
    pd.DataFrame(columns=[
        'Time', 'Signal', 'Stock_Code', 'Stock_Name', 'Stock_Type', 'Price', 'Change_Percent',
        'Main_Flow', 'Main_Ratio', 'Super_Flow', 'Super_Ratio',
        'Big_Flow', 'Big_Ratio', 'Mid_Flow', 'Mid_Ratio', 'Small_Flow', 'Small_Ratio'
    ]).to_csv(RECORD_FILE, index=False, encoding='utf-8-sig')

if not os.path.exists(NOTIFICATION_FILE):
    pd.DataFrame(columns=[
        'Time', 'Stock_Code', 'Signal_Type', 'Main_Flow', 'Main_Ratio', 'Message'
    ]).to_csv(NOTIFICATION_FILE, index=False, encoding='utf-8-sig')


def get_stock_type(code, name):
    """判断股票类型和标识"""
    code = str(code).zfill(6)
    prefix = code[:3]
    suffix = name[-2:] if len(name) >= 2 else ''

    if code.startswith('60'):
        base_type = '沪市主板'
    elif code.startswith('00'):
        base_type = '深市主板'
    elif code.startswith('30'):
        base_type = '创业板'
    elif code.startswith('688'):
        base_type = '科创板'
    elif code.startswith(('43', '83', '87')):
        base_type = '新三板'
    elif code.startswith(('8', '4', '920')):
        base_type = '北交所'
    else:
        base_type = '其他'

    identifiers = []
    if 'N' in suffix:
        identifiers.append('新股')
    if 'C' in suffix:
        identifiers.append('次新股<5日')
    if '次' in suffix:
        identifiers.append('次新股<1年')
    if 'XR' in suffix:
        identifiers.append('除权')
    if 'XD' in suffix:
        identifiers.append('除息')
    if 'DR' in suffix:
        identifiers.append('除权除息')
    if 'ST' in name or '*ST' in name or 'S*ST' in name or 'SST' in name or 'NST' in name:
        identifiers.append('ST')
    if 'R' in suffix:
        identifiers.append('融资融券')
    if 'K' in suffix:
        identifiers.append('科创板')
    if 'U' in suffix:
        identifiers.append('未完成')
    if 'S' in suffix and 'ST' not in name:
        identifiers.append('未股改')

    if identifiers:
        return f"{base_type}+{'+'.join(identifiers)}"
    return base_type


def send_bark_notification(message, stock_code, signal_type, main_flow=0.0, main_ratio=0.0):
    """发送 Bark 推送并记录推送历史，避免重复推送（根据主力净占比增幅判断）"""
    global TODAY_NOTIFICATION_CACHE

    # --- 新增开始：确保 stock_code 格式统一 ---
    stock_code = str(stock_code).zfill(6)
    # --- 新增结束 ---

    try:
        logging.debug(f"Bark 推送使用系统代理: {BARK_URL}")

        if signal_type in ['Error', 'Warning']:
            logging.info(f"正在准备推送一个 {signal_type} 类型的通知，内容: {message}")

        cache_key = (stock_code, signal_type)

        if cache_key in TODAY_NOTIFICATION_CACHE:
            if signal_type in ['Buy', 'Sell']:
                last_main_ratio = TODAY_NOTIFICATION_CACHE[cache_key]['main_ratio']
                if main_ratio > last_main_ratio:
                    ratio_increase = main_ratio - last_main_ratio
                    if ratio_increase < 10.0:
                        logging.info(
                            f"跳过重复推送: {stock_code} {signal_type}。主力净占比从 {last_main_ratio:.2f}% 增至 {main_ratio:.2f}%，增幅({ratio_increase:.2f}%)未超过10个点。")
                        return
                    else:
                        logging.info(
                            f"推送因增幅超过10个点: {stock_code} {signal_type}。主力净占比从 {last_main_ratio:.2f}% 增至 {main_ratio:.2f}%。")
                else:
                    logging.info(
                        f"跳过重复推送: {stock_code} {signal_type}。主力净占比未增加 (当前: {main_ratio:.2f}%, 上次: {last_main_ratio:.2f}%)。")
                    return
            elif signal_type in ['BigDeal', 'RankChange', 'Acceleration']:
                if signal_type != 'Acceleration':
                    logging.info(f"跳过重复推送: {stock_code} 的 {signal_type} 信号今天已推送过。")
                    return

        # --- 修改开始：修复 Bark 请求格式 ---
        # 方案1：尝试 GET 请求格式
        try:
            encoded_message = urllib.parse.quote(message, safe='')
            get_url = f"{BARK_URL}/{encoded_message}"
            response = requests.get(get_url, timeout=10)
        except Exception as get_error:
            logging.warning(f"GET 请求失败，尝试 POST 请求: {get_error}")
            # 方案2：如果 GET 失败，尝试改进的 POST 请求
            # 直接发送纯文本而不是 JSON
            response = requests.post(BARK_URL, data=message.encode('utf-8'),
                                   headers={"Content-Type": "text/plain; charset=utf-8"},
                                   timeout=10)
        # --- 修改结束 ---

        if response.status_code == 200:
            logging.info(f"Bark 推送成功: {message[:50]}...")
            print(f"Bark 推送成功: {message[:50]}...")
            if signal_type == 'Acceleration':
                TODAY_NOTIFICATION_CACHE[cache_key] = {'timestamp': datetime.now()}
            else:
                TODAY_NOTIFICATION_CACHE[cache_key] = {'main_ratio': main_ratio}

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            new_record = pd.DataFrame([{
                'Time': current_time,
                'Stock_Code': stock_code,
                'Signal_Type': signal_type,
                'Main_Flow': main_flow,
                'Main_Ratio': main_ratio,
                'Message': message
            }])
            new_record.to_csv(NOTIFICATION_FILE, mode='a',
                              header=not os.path.exists(NOTIFICATION_FILE) or os.path.getsize(NOTIFICATION_FILE) == 0,
                              index=False, encoding='utf-8-sig',
                              columns=['Time', 'Stock_Code', 'Signal_Type', 'Main_Flow', 'Main_Ratio', 'Message'])
        else:
            # --- 修改开始：增强错误日志 ---
            logging.error(f"Bark 推送失败: 状态码={response.status_code}, 响应内容={response.text}")
            print(f"Bark 推送失败: 状态码={response.status_code}, 响应内容={response.text}")
            # --- 修改结束 ---

    except Exception as e:
        # --- 修改开始：增强异常日志 ---
        logging.error(f"Bark 推送错误: {str(e)}")
        print(f"Bark 推送错误: {str(e)}")
        # --- 修改结束 ---


# 【替换】请在 get_all_capital_flow_east.py 文件中找到并替换此函数

def convert_to_float(x):
    """
    (修正版) 将包含中文单位（亿、万）和百分号的字符串转换为浮点数。
    """
    try:
        if pd.isna(x):
            return 0.0

        # 如果已经是数字，直接返回
        if isinstance(x, (int, float)):
            return float(x)

        # 转换为字符串处理
        s = str(x).strip()

        # 处理百分比
        if '%' in s:
            return float(s.replace('%', ''))

        # 处理中文单位
        if '亿' in s:
            return float(s.replace('亿', '')) * 100000000
        if '万' in s:
            return float(s.replace('万', '')) * 10000

        # 尝试直接转换
        return float(s)

    except (ValueError, TypeError):
        return 0.0


def is_trading_time():
    """判断当前时间是否在A股交易时间（9:25:00-11:30:00，13:00:00-15:00:00）"""
    # 如果交易时间检查开关关闭，则始终返回True
    if not ENABLE_TRADING_TIME_CHECK:
        return True

    now = datetime.now().time()
    morning_start = time(9, 25)  # 早上9点25开始获取数据
    morning_end = time(11, 30)   # 11点30收盘不获取数据
    afternoon_start = time(13, 0)  # 13点开盘获取数据
    afternoon_end = time(15, 0)    # 15点收盘不获取数据
    return (morning_start <= now < morning_end) or (afternoon_start <= now < afternoon_end)


def is_trading_day():
    """判断当前日期是否为交易日，DEBUG模式下始终返回True"""
    if DEBUG_MODE:
        logging.debug("DEBUG模式启用，视为交易日")
        return True
    try:
        current_date = datetime.now().date()
        current_year = current_date.year

        if current_year >= 2025:
            is_weekday = current_date.weekday() < 5
            logging.debug(f"2025 年交易日判断: {current_date} 是工作日: {is_weekday}")
            return is_weekday

        trade_dates_df = ak.tool_trade_date_hist_sina()
        trade_dates = pd.to_datetime(trade_dates_df['trade_date']).dt.date
        is_trading = current_date in trade_dates
        logging.debug(f"交易日判断: {current_date} 在 akshare 日历中: {is_trading}")
        return is_trading
    except Exception as e:
        logging.error(f"获取交易日历失败: {e}")
        print(f"获取交易日历失败: {e}")
        send_bark_notification(f"股票监控脚本错误: 获取交易日历失败 {e}", stock_code="000000", signal_type="Error")
        return False


def write_ebk_file_safely(file_path, new_codes, overwrite=False):
    """
    (线程安全) 创建或更新EBK文件。
    :param file_path: 文件路径
    :param new_codes: 本次要写入的新股票代码列表
    :param overwrite: 是否覆盖文件。True=覆盖(用于买入)，False=合并(用于卖出)
    """
    with file_lock:
        try:
            final_codes = set(new_codes)
            if not overwrite:
                # 合并模式：读取旧代码
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_codes = {line.strip() for line in f if line.strip()}
                        final_codes.update(existing_codes)

            sorted_codes = sorted(list(final_codes))

            logging.debug(
                f"写入EBK文件: {file_path}, 模式: {'覆盖' if overwrite else '合并'}, 代码数: {len(sorted_codes)}")

            # 只有当有股票代码时才写入文件
            if sorted_codes:
                with open(file_path, 'wb') as f:
                    for stock_code in sorted_codes:
                        stock_code_formatted = f'\r\n{stock_code}'
                        f.write(stock_code_formatted.encode('utf-8'))
                    f.flush()

                action = "覆盖" if overwrite else "更新"
                logging.info(f"成功{action}EBK文件: {file_path}，包含 {len(sorted_codes)} 条股票代码")
                print(f"成功{action}EBK文件: {file_path}，包含 {len(sorted_codes)} 条股票代码")
            else:
                # 如果没有股票代码，不清空文件，只记录日志
                logging.debug(f"无股票代码需要写入EBK文件: {file_path}，保持文件现状")

        except Exception as e:
            logging.error(f"写入EBK文件失败: {file_path}, 错误: {e}")
            print(f"写入EBK文件失败: {file_path}, 错误: {e}")


def print_historical_data(stock_code):
    """查询数据库并打印股票的历史资金流向数据"""
    logging.info(f"--- 正在为股票 {stock_code} 打印历史资金流向数据 ---")
    try:
        conn = sqlite3.connect('fund_flow.db')

        # 检查数据是否存在
        base_df = pd.read_sql_query(f"SELECT * FROM daily_fund_flow WHERE stock_code='{stock_code}'", conn)
        if base_df.empty:
            logging.warning(f"数据库中没有 {stock_code} 的数据，无法打印历史数据。")
            conn.close()
            return

        print("\n" + f"--- 股票代码: {stock_code} 历史资金流向分析 ---" + "\n")

        # 1. 打印上涨股票平均指标
        stats_up_df = pd.read_sql_query(f"SELECT * FROM stock_stats_up WHERE stock_code='{stock_code}'", conn)
        if not stats_up_df.empty:
            stats = stats_up_df.iloc[0]
            print(f"上涨股票平均指标（涨跌幅>0，共 {stats['record_count']}个交易日）：")
            print(f"记录数：{stats['record_count']}")
            print(f"平均主力净流入净额：{format_amount(stats['avg_main_net_inflow'])}")
            print(f"平均主力净占比：{stats['avg_main_net_ratio']:.2f}%")
            print(f"平均超大单净流入净额：{format_amount(stats['avg_super_large_net_inflow'])}")
            print(f"平均超大单净占比：{stats['avg_super_large_net_ratio']:.2f}%")
            print(f"平均大单净流入净额：{format_amount(stats['avg_large_net_inflow'])}")
            print(f"平均大单净占比：{stats['avg_large_net_ratio']:.2f}%")
            print(f"平均中单净流入净额：{format_amount(stats['avg_medium_net_inflow'])}")
            print(f"平均中单净占比：{stats['avg_medium_net_ratio']:.2f}%")
            print(f"平均小单净流入净额：{format_amount(stats['avg_small_net_inflow'])}")
            print(f"平均小单净占比：{stats['avg_small_net_ratio']:.2f}%")

        # 2. 打印下跌股票平均指标
        stats_down_df = pd.read_sql_query(f"SELECT * FROM stock_stats_down WHERE stock_code='{stock_code}'", conn)
        if not stats_down_df.empty:
            stats = stats_down_df.iloc[0]
            print(f"\n下跌股票平均指标（涨跌幅<0，共 {stats['record_count']}个交易日）：")
            print(f"记录数：{stats['record_count']}")
            print(f"平均主力净流入净额：{format_amount(stats['avg_main_net_inflow'])}")
            print(f"平均主力净占比：{stats['avg_main_net_ratio']:.2f}%")
            print(f"平均超大单净流入净额：{format_amount(stats['avg_super_large_net_inflow'])}")
            print(f"平均超大单净占比：{stats['avg_super_large_net_ratio']:.2f}%")
            print(f"平均大单净流入净额：{format_amount(stats['avg_large_net_inflow'])}")
            print(f"平均大单净占比：{stats['avg_large_net_ratio']:.2f}%")
            print(f"平均中单净流入净额：{format_amount(stats['avg_medium_net_inflow'])}")
            print(f"平均中单净占比：{stats['avg_medium_net_ratio']:.2f}%")
            print(f"平均小单净流入净额：{format_amount(stats['avg_small_net_inflow'])}")
            print(f"平均小单净占比：{stats['avg_small_net_ratio']:.2f}%")

        # 3. 打印特定涨跌幅资金净额
        threshold_df = pd.read_sql_query(f"SELECT * FROM stock_stats_threshold WHERE stock_code='{stock_code}'", conn)
        if not threshold_df.empty:
            print("\n--- 特定涨跌幅资金表现 ---")
            up_10 = threshold_df[threshold_df['threshold_type'] == 'up_10']
            if not up_10.empty:
                stats = up_10.iloc[0]
                dates = base_df[base_df['change_pct'] >= 10]['date'].tolist()
                print(f"特定涨幅资金净额（涨幅>=10%，{stats['record_count']}个交易日：{', '.join(dates)}）：")
                print(f"记录数：{stats['record_count']}")
                print(f"平均主力净流入净额：{format_amount(stats['avg_main_net_inflow'])}")
                print(f"平均超大单净流入净额：{format_amount(stats['avg_super_large_net_inflow'])}")
                print(f"平均大单净流入净额：{format_amount(stats['avg_large_net_inflow'])}")

            down_99 = threshold_df[threshold_df['threshold_type'] == 'down_9.9']
            if not down_99.empty:
                stats = down_99.iloc[0]
                dates = base_df[base_df['change_pct'] <= -9.9]['date'].tolist()
                print(f"特定跌幅资金净额（跌幅>=9.9%，{stats['record_count']}个交易日：{', '.join(dates)}）：")
                print(f"记录数：{stats['record_count']}")
                print(f"平均主力净流入净额：{format_amount(stats['avg_main_net_inflow'])}")
                print(f"平均超大单净流入净额：{format_amount(stats['avg_super_large_net_inflow'])}")
                print(f"平均大单净流入净额：{format_amount(stats['avg_large_net_inflow'])}")

        # 4. 打印最大/最小值
        extremes_df = pd.read_sql_query(f"SELECT * FROM stock_stats_extremes WHERE stock_code='{stock_code}'", conn)
        if not extremes_df.empty:
            print("\n--- 历史资金指标极值 ---")
            metrics_map = {
                'main_net_inflow': '主力净流入净额',
                'main_net_ratio': '主力净占比',
                'super_large_net_inflow': '超大单净流入净额',
                'super_large_net_ratio': '超大单净占比',
                'large_net_inflow': '大单净流入净额',
                'large_net_ratio': '大单净占比',
                'medium_net_inflow': '中单净流入净额',
                'medium_net_ratio': '中单净占比',
                'small_net_inflow': '小单净流入净额',
                'small_net_ratio': '小单净占比'
            }
            for metric, name in metrics_map.items():
                extreme_data = extremes_df[extremes_df['metric'] == metric]
                if not extreme_data.empty:
                    data = extreme_data.iloc[0]
                    formatter = format_amount if 'inflow' in metric else lambda x: f"{x:.2f}%"
                    print(f"{name}：")
                    print(f"  最大值：{formatter(data['max_value'])}（日期：{data['max_date']}）")
                    print(f"  最小值：{formatter(data['min_value'])}（日期：{data['min_date']}）")

        conn.close()
        print("\n" + "--- 历史数据结束 ---" + "\n")
    except Exception as e:
        logging.error(f"打印 {stock_code} 的历史数据失败: {e}")
        print(f"打印 {stock_code} 的历史数据失败: {e}")


# --- 可复用的卖出条件评估函数 ---
# --- 可复用的卖出条件评估函数 ---
# --- 可复用的卖出条件评估函数 ---
def evaluate_sell_condition(main_net, main_ratio, super_net, super_ratio, large_net, large_ratio, medium_net,
                            medium_ratio, small_net, small_ratio, historical_data):
    """
    根据输入的资金流数据和历史数据，评估是否满足卖出条件。
    返回: 一个元组 (is_sell_signal, reasons)。
          is_sell_signal: 布尔值，True表示满足卖出条件。
          reasons: 列表，包含满足条件的具体原因。
    """
    if not historical_data.empty:
        min_main_net = historical_data['main_net_inflow'].min()
        down_days = historical_data[historical_data['change_pct'] < 0]
        avg_down_main_net = down_days['main_net_inflow'].mean() if not down_days.empty else 0
        limit_down_days = historical_data[historical_data['change_pct'] <= -9.9]
        avg_limit_down_main_net = limit_down_days['main_net_inflow'].mean() if not limit_down_days.empty else 0
    else:
        min_main_net = avg_down_main_net = avg_limit_down_main_net = 0

    retail_inflow = (medium_net if medium_net > 0 else 0) + (small_net if small_net > 0 else 0)
    sell_conditions = {
        '主力净流出>5000万或净占比<-5%': main_net < -5000 or main_ratio < -5,

        '超大单净占比<-5%且大单净占比<-2%': super_ratio < -5 and large_ratio < -2,
        '全为流出且主力流出>散户流入': (
                super_ratio < 0 and large_ratio < 0 and medium_net < 0 and small_net < 0 and
                retail_inflow > 0 and abs(main_net) > retail_inflow
        ),
        '至少三个净占比为负': (
                sum(ratio < 0 for ratio in
                    [main_ratio, super_ratio, large_ratio, medium_ratio]) >= 3
        ),
        '主力及超大单净占比为负且流出>5000万': (
                main_ratio < 0 and super_ratio < 0 and main_net < -5000 and super_net < -5000
        ),
        '至少两个净占比为负且主力/超大单/大单净额较大': (
                sum(ratio < 0 for ratio in
                    [main_ratio, super_ratio, large_ratio, medium_ratio, small_ratio]) >= 2 and
                (
                        (main_ratio < 0 and main_net < -5000) or
                        (super_ratio < 0 and abs(super_net) > abs(main_net)) or
                        (large_ratio < 0 and abs(large_net) > abs(main_net))
                )
        )
    }
    additional_sell_conditions = {
        '主力净流出绝对值>历史最大流出': abs(main_net) > abs(
            min_main_net) if main_net < 0 and min_main_net < 0 else False,
        '主力净流出绝对值>跌停日均值2倍': abs(main_net) > 2 * abs(
            avg_limit_down_main_net) if main_net < 0 and avg_limit_down_main_net < 0 else False,
        '主力净流出>下跌日均值': abs(main_net) > abs(
            avg_down_main_net) if main_net < 0 and avg_down_main_net < 0 else False
    }
    sell_conditions_map = {
        12: all(sell_conditions.values()),
        1: sell_conditions['主力净流出>5000万或净占比<-5%'],
        2: sell_conditions['超大单净占比<-5%且大单净占比<-2%'],
        3: sell_conditions['全为流出且主力流出>散户流入'],
        4: sell_conditions['至少三个净占比为负'] and (additional_sell_conditions[
                                                          '主力净流出>下跌日均值'] if not historical_data.empty else True),
        5: sell_conditions['主力及超大单净占比为负且流出>5000万'],
        6: sell_conditions['至少两个净占比为负且主力/超大单/大单净额较大'] and (
            additional_sell_conditions[
                '主力净流出>下跌日均值'] if not historical_data.empty else True),
        7: additional_sell_conditions['主力净流出绝对值>历史最大流出'] or
           additional_sell_conditions['主力净流出绝对值>跌停日均值2倍'],
        8: additional_sell_conditions['主力净流出>下跌日均值']
    }

    hit_reasons = [f"条件{i}" for i in ACTIVE_SELL_CONDITIONS if sell_conditions_map.get(i, False)]

    if hit_reasons:
        return True, hit_reasons
    else:
        # 【核心修改点】: 返回一个简洁的提示，而不是所有未满足的条件
        return False, ["未满足任何激活的卖出条件"]


# --- 任务一：检查持仓股的卖出信号 (可独立调度) ---
# --- 任务一：检查持仓股的卖出信号 (可独立调度) ---
# --- 任务一：检查持仓股的卖出信号 (可独立调度) ---
def task_scan_positions_for_sell_signals():
    """
    (独立任务) 读取持仓文件，为每只股票获取数据并判断卖出信号。
    【已集成每日名称缓存】
    【V2.0 升级：新增龙头豁免机制】
    """
    global is_positions_scan_running, STOCK_NAME_CACHE, CACHE_DATE, STRONG_SECTORS_LIST

    if is_positions_scan_running:
        logging.warning("持仓股检查任务仍在运行中，跳过本次调度。")
        print("持仓股检查任务仍在运行中，跳过本次调度。")
        return

    if ENABLE_TRADING_TIME_CHECK and not is_trading_time():
        current_time = datetime.now().strftime('%H:%M:%S')
        msg = f"非交易时间（当前时间: {current_time}），跳过持仓股检查任务。交易时间: 9:25-11:30, 13:00-15:00"
        logging.info(msg)
        print(msg)
        return

    is_positions_scan_running = True
    try:
        today_str = datetime.now().strftime('%Y%m%d')
        if CACHE_DATE != today_str:
            logging.info(f"新的一天 ({today_str})，正在清空旧的股票名称缓存。")
            print(f"新的一天 ({today_str})，正在清空旧的股票名称缓存。")
            STOCK_NAME_CACHE = {}
            CACHE_DATE = today_str

        logging.info("--- [任务开始] 检查持仓股票的卖出信号 ---")
        print("\n--- [任务开始] 检查持仓股票的卖出信号 ---")
        positions_file = 'D:/current_positions.txt'
        sell_signals = []
        sell_details = []

        checked_count = 0
        sell_count = 0
        no_sell_count = 0
        error_count = 0

        if not os.path.exists(positions_file):
            logging.warning("持仓文件 D:/current_positions.txt 不存在，跳过持仓检查。")
            print("持仓文件 D:/current_positions.txt 不存在，跳过持仓检查。")
            return

        with open(positions_file, 'r', encoding='utf-8') as f:
            held_stocks = [line.strip() for line in f if line.strip()]

        if not held_stocks:
            logging.info("持仓文件为空，不执行持仓股卖出检查。")
            print("持仓文件为空，不执行持仓股卖出检查。")
            return

        # --- 【核心修改-1】: 在任务开始时，获取一次全市场排名和板块数据，用于龙头判断 ---
        stock_df = pd.DataFrame()
        stock_board_concept_name_ths_df = pd.DataFrame()
        try:
            stock_df = ak.stock_individual_fund_flow_rank(indicator="今日")
            if not stock_df.empty:
                stock_df['排名'] = range(1, len(stock_df) + 1)
                stock_df['代码'] = stock_df['代码'].astype(str).str.zfill(6)
            stock_board_concept_name_ths_df = ak.stock_board_concept_name_ths()
        except Exception as e:
            logging.error(f"持仓检查任务中获取市场快照失败: {e}，龙头豁免功能将受限。")
        # --- 修改结束 ---

        total_stocks = len(held_stocks)
        logging.info(f"读取到 {total_stocks} 只持仓股票，开始逐个分析...")
        print(f"读取到 {total_stocks} 只持仓股票，开始逐个分析...")

        for stock_code_full in held_stocks:
            checked_count += 1
            stock_name = ''
            try:
                stock_code_6_digit = stock_code_full.split('.')[0]
                market = 'sh' if stock_code_6_digit.startswith('6') else 'sz'

                if stock_code_6_digit in STOCK_NAME_CACHE:
                    stock_name = STOCK_NAME_CACHE[stock_code_6_digit]
                else:
                    try:
                        info_dict = ak.stock_individual_info_em(symbol=stock_code_6_digit)
                        stock_name = info_dict.get('name', '')
                        if stock_name:
                            STOCK_NAME_CACHE[stock_code_6_digit] = stock_name
                            logging.info(f"已缓存新股票名称: {stock_code_6_digit} -> {stock_name}")
                    except Exception as e_info:
                        logging.warning(f"为 {stock_code_6_digit} 获取并缓存股票名称失败: {e_info}")

                # --- 【核心修改-2】: 龙头豁免逻辑 ---
                is_dragon_head = False
                try:
                    if not stock_df.empty and not stock_board_concept_name_ths_df.empty:
                        stock_rank_info = stock_df[stock_df['代码'] == stock_code_6_digit]
                        if not stock_rank_info.empty:
                            rank = stock_rank_info.iloc[0]['排名']
                            stock_concepts = stock_board_concept_name_ths_df[
                                stock_board_concept_name_ths_df['代码'] == stock_code_6_digit]['概念名称'].tolist()
                            is_in_strong_sector = any(concept in STRONG_SECTORS_LIST for concept in stock_concepts)

                            # 定义龙头的标准：排名前5，且属于强势板块
                            if rank <= 5 and is_in_strong_sector:
                                is_dragon_head = True
                except Exception as e_dragon:
                    logging.warning(f"判断 {stock_code_6_digit} 龙头地位时出错: {e_dragon}。将按普通股处理。")

                if is_dragon_head:
                    log_msg = f"【龙头豁免】: 股票 {stock_code_full} ({stock_name}) 被识别为市场龙头 (排名<=5 且属强势板块)，跳过本次卖出检查。"
                    logging.info(log_msg)
                    print(log_msg)
                    no_sell_count += 1
                    continue  # 直接跳到下一个持仓股，不执行后续的卖出判断
                # --- 修改结束 ---

                df_flow = ak.stock_individual_fund_flow(stock=stock_code_6_digit, market=market)
                if df_flow.empty:
                    logging.warning(f"无法获取持仓股 {stock_code_full} ({stock_name}) 的资金流数据。")
                    error_count += 1
                    continue

                latest_flow = df_flow.iloc[-1]

                main_net = convert_to_float(latest_flow['主力净流入-净额']) / 10000
                main_ratio = convert_to_float(latest_flow['主力净流入-净占比'])
                super_net = convert_to_float(latest_flow['超大单净流入-净额']) / 10000
                super_ratio = convert_to_float(latest_flow['超大单净流入-净占比'])
                large_net = convert_to_float(latest_flow['大单净流入-净额']) / 10000
                large_ratio = convert_to_float(latest_flow['大单净流入-净占比'])
                medium_net = convert_to_float(latest_flow['中单净流入-净额']) / 10000
                medium_ratio = convert_to_float(latest_flow['中单净流入-净占比'])
                small_net = convert_to_float(latest_flow['小单净流入-净额']) / 10000
                small_ratio = convert_to_float(latest_flow['小单净流入-净占比'])

                conn = sqlite3.connect('fund_flow.db')
                historical_data = pd.read_sql_query(
                    f"SELECT * FROM daily_fund_flow WHERE stock_code='{stock_code_6_digit}'", conn)
                conn.close()

                is_sell, reasons = evaluate_sell_condition(main_net, main_ratio, super_net, super_ratio, large_net,
                                                           large_ratio, medium_net, medium_ratio, small_net,
                                                           small_ratio,
                                                           historical_data)

                if is_sell:
                    sell_count += 1
                    reason_str = "; ".join(reasons)
                    log_msg = f"【持仓卖出信号】: 股票 {stock_code_full} ({stock_name}) 满足卖出条件: [{reason_str}]"
                    logging.info(log_msg)
                    print(log_msg)
                    sell_signals.append(stock_code_6_digit)
                    sell_details.append(f"{stock_code_full} ({stock_name}): [{reason_str}]")
                else:
                    no_sell_count += 1
                    logging.info(f"【持仓状态更新】: 股票 {stock_code_full} ({stock_name}) 未触发卖出。")

            except Exception as e:
                error_count += 1
                logging.error(f"处理持仓股 {stock_code_full} ({stock_name}) 时发生错误: {e}")
                print(f"处理持仓股 {stock_code_full} ({stock_name}) 时发生错误: {e}")
                continue

        if sell_signals:
            write_signal_file_atomically('D:/flow_sell.ebk', sell_signals, overwrite=False)

        print("\n" + "=" * 20 + " 持仓股检查报告 " + "=" * 20)
        report_msg = (
            f"报告摘要: 共检查 {checked_count} 个股票 | "
            f"卖出信号 {sell_count} 个 | "
            f"无信号 {no_sell_count} 个 | "
            f"失败 {error_count} 个。"
        )
        print(report_msg)
        logging.info(report_msg)

        if sell_details:
            print("--- 卖出信号详情 ---")
            for detail in sell_details:
                print(detail)
        print("=" * 58)

    except Exception as e:
        logging.error(f"读取或处理持仓文件 {positions_file} 失败: {e}")
        print(f"读取或处理持仓文件 {positions_file} 失败: {e}")
    finally:
        is_positions_scan_running = False
        logging.info("--- [任务结束] 持仓股检查完成 (状态锁已释放) ---")


# --- 任务三：收盘后获取大盘资金流数据 (独立调度) ---
def task_get_market_fund_flow():
    """
    (独立任务) 收盘后获取大盘资金流数据，每日15点后执行一次。
    """
    try:
        logging.info("--- [任务开始] 获取大盘资金流数据 ---")
        print("\n--- [任务开始] 获取大盘资金流数据 ---")

        date_folder = get_date_folder()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 获取大盘资金流
        try:
            market_fund_flow_df = ak.stock_market_fund_flow()
            if not market_fund_flow_df.empty:
                market_file = os.path.join(date_folder, f'market_fund_flow_{timestamp}.csv')
                market_fund_flow_df.to_csv(market_file, index=False, encoding='utf-8-sig')
                logging.info(f"成功保存大盘资金流数据到: {market_file}")
                print(f"成功保存大盘资金流数据到: {market_file}")
            else:
                logging.warning("获取到的大盘资金流数据为空")
                print("获取到的大盘资金流数据为空")
        except Exception as e:
            logging.error(f"获取大盘资金流数据失败: {e}")
            print(f"获取大盘资金流数据失败: {e}")

        logging.info("--- [任务结束] 大盘资金流数据获取完成 ---")
        print("--- [任务结束] 大盘资金流数据获取完成 ---")

    except Exception as e:
        logging.error(f"执行大盘资金流数据获取任务时发生错误: {e}")
        print(f"执行大盘资金流数据获取任务时发生错误: {e}")


# ==============================================================================
# --- 【新功能】盘中异动分析模块 (独立函数) ---
# ==============================================================================
def format_currency_scan(amount):
    if not isinstance(amount, (int, float, np.number)): return str(amount)
    if abs(amount) >= 1_0000_0000: return f"{amount / 1_0000_0000:.1f}亿"
    if abs(amount) >= 1_0000: return f"{amount / 1_0000:.1f}万"
    return str(int(amount))


def get_trend_slope_scan(series_y, series_x):
    if len(series_y) < 2: return 0
    # 过滤掉NaN值
    y_clean = pd.Series(series_y).dropna()
    if len(y_clean) < 2: return 0
    # 确保x和y的长度一致
    if len(y_clean) > len(series_x): return 0
    # --- 核心修复：使用 .loc 进行标签索引，而不是 .iloc 进行位置索引 ---
    # 这可以防止在处理具有非顺序索引的DataFrame切片时发生越界错误。
    x_clean = series_x.loc[y_clean.index]
    model = LinearRegression()
    X = np.array([(t.hour * 3600 + t.minute * 60 + t.second) for t in x_clean]).reshape(-1, 1)
    y = np.array(y_clean).reshape(-1, 1)
    model.fit(X, y)
    return model.coef_[0][0] * 60


def load_and_combine_data_scan(directory):
    all_files = [f for f in os.listdir(directory) if f.startswith('fund_flow_rank_') and f.endswith('.csv')]
    if not all_files: return None
    df_list = []
    pattern = re.compile(r'fund_flow_rank_(\d{8})_(\d{6})\.csv')
    for filename in sorted(all_files):
        match = pattern.search(filename)
        if match:
            timestamp_str = match.group(2)
            timestamp_dt = datetime.strptime(timestamp_str, '%H%M%S').time()
            try:
                df = pd.read_csv(os.path.join(directory, filename), encoding='utf-8-sig')
                df['timestamp'] = timestamp_dt
                df_list.append(df)
            except Exception:
                pass
    if not df_list: return None
    master_df = pd.concat(df_list, ignore_index=True)
    master_df.columns = [c.strip() for c in master_df.columns]
    master_df['代码'] = master_df['代码'].apply(lambda x: f"{x:06d}" if isinstance(x, int) else str(x).zfill(6))
    amount_cols = [col for col in master_df.columns if '净额' in col]
    for col in amount_cols: master_df[col] = pd.to_numeric(master_df[col], errors='coerce').fillna(0)
    master_df.sort_values(by=['代码', 'timestamp'], inplace=True)
    return master_df


def find_breakouts(df_period, period_name, all_day_df):
    """(已升级) 寻找异动拉升股票，包含二次加速逻辑。
       【修改】: 返回一个包含异动股票代码的列表。
    """
    breakouts = {}
    breakout_codes = []  # 【新增】用于存储异动股票代码的列表
    if df_period.empty:
        return breakouts, breakout_codes  # 【修改】返回两个值

    last_timestamp = df_period['timestamp'].max()
    top_stocks_snapshot = df_period[df_period['timestamp'] == last_timestamp].nlargest(200, '今日主力净流入-净额')
    unique_codes = top_stocks_snapshot['代码'].unique()

    for code in unique_codes:
        traj_period = df_period[df_period['代码'] == code].copy().sort_values('timestamp').reset_index(drop=True)
        if len(traj_period) < 3: continue

        # --- 特征工程 ---
        rank_trend_slope = get_trend_slope_scan(traj_period['排名'], traj_period['timestamp'])
        inflow_trend_slope = get_trend_slope_scan(traj_period['今日主力净流入-净额'], traj_period['timestamp'])

        am_traj = all_day_df[(all_day_df['代码'] == code) & (all_day_df['timestamp'] <= AM_END_TIME)]
        am_avg_rank = am_traj['排名'].mean() if not am_traj.empty else 999
        if pd.isna(am_avg_rank): am_avg_rank = 999

        # --- 规则引擎 ---
        is_breakout = False
        if period_name == "上午盘":
            early_avg_rank = traj_period[traj_period['timestamp'] < time(9, 50)]['排名'].mean() if not traj_period[
                traj_period['timestamp'] < time(9, 50)].empty else 999
            if pd.isna(early_avg_rank): early_avg_rank = 999
            if (early_avg_rank > BREAKOUT_INITIAL_AVG_RANK and
                    len(traj_period) > 0 and traj_period.iloc[-1]['排名'] <= BREAKOUT_FINAL_RANK and
                    rank_trend_slope < BREAKOUT_RANK_TREND_MIN_SLOPE and
                    inflow_trend_slope > BREAKOUT_INFLOW_TREND_MIN_SLOPE):
                is_breakout = True

        elif period_name == "下午盘":
            # 模式A: 从潜伏到爆发
            if (am_avg_rank > BREAKOUT_INITIAL_AVG_RANK and
                    len(traj_period) > 0 and traj_period.iloc[-1]['排名'] <= BREAKOUT_FINAL_RANK):
                is_breakout = True

            # 模式B: 强者再加速
            else:
                am_inflow_slope = get_trend_slope_scan(am_traj['今日主力净流入-净额'], am_traj['timestamp']) if len(
                    am_traj) > 2 else 0
                if (inflow_trend_slope > PM_ACCELERATION_MIN_SLOPE and
                        (
                                am_inflow_slope <= 0 or inflow_trend_slope > am_inflow_slope * PM_ACCELERATION_FACTOR)):  # 增加对上午流出的判断
                    is_breakout = True
                    traj_period.attrs[
                        'breakout_reason'] = f"二次加速(下午资金斜率{format_currency_scan(inflow_trend_slope)}/分)"

        if is_breakout:
            initial_rank = traj_period.iloc[0]['排名']
            breakouts[code] = {'trajectory': traj_period, 'initial_rank': initial_rank}
            breakout_codes.append(code)  # 【新增】将代码添加到列表中

    return breakouts, breakout_codes  # 【修改】返回两个值


def run_breakout_scan():
    """【修改】执行盘中异动扫描的主函数，并更新全局异动列表"""
    global BREAKOUT_STOCKS_LIST  # 【新增】声明使用全局变量
    try:
        today_str = datetime.now().strftime('%Y%m%d')
        target_dir = os.path.join('data', today_str)

        if not os.path.exists(target_dir):
            logging.debug(f"今日数据文件夹尚未创建，跳过异动扫描: {target_dir}")
            BREAKOUT_STOCKS_LIST = []  # 【新增】清空列表
            return

        all_day_df = load_and_combine_data_scan(target_dir)
        if all_day_df is None or all_day_df.empty:
            logging.debug("当前无分钟级数据，跳过异动扫描。")
            BREAKOUT_STOCKS_LIST = []  # 【新增】清空列表
            return

        current_time = datetime.now().time()
        if current_time <= AM_END_TIME:
            period_name = "上午盘"
            df_period = all_day_df
        else:
            period_name = "下午盘"
            df_period = all_day_df[all_day_df['timestamp'] >= PM_START_TIME]

        if df_period.empty:
            BREAKOUT_STOCKS_LIST = []  # 【新增】清空列表
            return

        breakouts, breakout_codes = find_breakouts(df_period, period_name, all_day_df)  # 【修改】接收两个返回值
        BREAKOUT_STOCKS_LIST = breakout_codes  # 【新增】更新全局列表

        # 这个函数的打印报告部分保持不变，所以省略
        # ...

    except Exception as e:
        logging.error(f"执行盘中异动扫描时发生错误: {e}")
        print(f"执行盘中异动扫描时发生错误: {e}")
        BREAKOUT_STOCKS_LIST = []  # 【新增】出错时清空


def task_analyze_strong_sectors():
    """
    【V6.5 动态量能版 · 共振升级】通过多因子评分模型分析并更新全局的强势和主线板块列表。
    【升级点】：引入动态量能基准，解决小资金板块得分虚高问题，使评分更具格局。
    """
    global STRONG_SECTORS_LIST, MAINLINE_SECTOR_LIST, SECTOR_STRENGTH_TRACKER, PREVIOUS_SECTOR_DATA
    global df_analysis_global  # 使用全局变量传递评分结果

    try:
        logging.info("--- [任务开始] 分析强势板块 (V6.5 动态量能版) ---")
        print("\n" + "=" * 20 + " 强势板块分析报告 (V6.5 动态量能版) " + "=" * 20)

        # 1. 分别获取行业与概念数据，并打上标签 (逻辑不变)
        all_sectors_list = []
        try:
            sector_df_em = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
            if not sector_df_em.empty and '名称' in sector_df_em.columns:
                sector_df_em = sector_df_em[['名称', '今日涨跌幅', '今日主力净流入-净额']].copy()
                sector_df_em['type'] = '行业'
                all_sectors_list.append(sector_df_em)
                logging.info(f"成功获取东方财富行业资金流数据 {len(sector_df_em)} 条")
        except Exception as e:
            logging.error(f"获取东方财富行业资金流失败: {e}")

        try:
            concept_df_ths = ak.stock_fund_flow_concept(symbol="即时")
            if not concept_df_ths.empty and '行业' in concept_df_ths.columns:
                concept_df_ths_std = concept_df_ths[['行业', '行业-涨跌幅', '净额']].copy()
                concept_df_ths_std.columns = ['名称', '今日涨跌幅', '今日主力净流入-净额']
                concept_df_ths_std['今日主力净流入-净额'] = concept_df_ths_std['今日主力净流入-净额'] * 100000000
                concept_df_ths_std['type'] = '概念'
                all_sectors_list.append(concept_df_ths_std)
                logging.info(f"成功合并同花顺概念资金流数据 {len(concept_df_ths_std)} 条")
        except Exception as e:
            logging.error(f"获取同花顺概念资金流失败: {e}")

        if not all_sectors_list:
            logging.warning("未能获取任何板块数据，强势板块分析中止。")
            STRONG_SECTORS_LIST, MAINLINE_SECTOR_LIST = [], []
            df_analysis_global = pd.DataFrame()
            return

        # 2. 合并数据并进行预处理 (逻辑不变)
        all_sectors_df = pd.concat(all_sectors_list, ignore_index=True).drop_duplicates(subset=['名称', 'type'])
        all_sectors_df['名称'] = all_sectors_df['名称'].str.strip()
        all_sectors_df['今日涨跌幅'] = all_sectors_df['今日涨跌幅'].apply(convert_to_float)
        all_sectors_df['今日主力净流入-净额'] = all_sectors_df['今日主力净流入-净额'].apply(convert_to_float)
        all_sectors_df.dropna(subset=['名称', '今日涨跌幅', '今日主力净流入-净额'], inplace=True)
        all_sectors_df.sort_values(by='今日主力净流入-净额', ascending=False, inplace=True)
        all_sectors_df.reset_index(drop=True, inplace=True)
        all_sectors_df['rank'] = all_sectors_df.index + 1

        # 3. 计算“资金加速度”和识别“主线挑战者” (逻辑不变)
        all_sectors_df['inflow_delta'] = 0.0
        challenger_sectors = set()
        if PREVIOUS_SECTOR_DATA:
            for index, row in all_sectors_df.iterrows():
                sector_name = row['名称']
                if sector_name in PREVIOUS_SECTOR_DATA:
                    previous_data = PREVIOUS_SECTOR_DATA[sector_name]
                    all_sectors_df.at[index, 'inflow_delta'] = row['今日主力净流入-净额'] - previous_data.get('inflow',
                                                                                                              0)
                    previous_rank = previous_data.get('rank', 999)
                    current_rank = row['rank']
                    if previous_rank > 20 and current_rank <= 10: challenger_sectors.add(sector_name)
                    if 2 <= current_rank <= 5:
                        prev_rank_inflow = \
                        all_sectors_df.loc[all_sectors_df['rank'] == current_rank - 1, '今日主力净流入-净额'].iloc[0]
                        current_gap = prev_rank_inflow - row['今日主力净流入-净额']
                        prev_sector_of_rank_N_minus_1 = [s for s, d in PREVIOUS_SECTOR_DATA.items() if
                                                         d.get('rank') == current_rank - 1]
                        if prev_sector_of_rank_N_minus_1:
                            prev_gap = PREVIOUS_SECTOR_DATA[prev_sector_of_rank_N_minus_1[0]].get('inflow',
                                                                                                  0) - previous_data.get(
                                'inflow', 0)
                            if prev_gap > 0 and current_gap < prev_gap * 0.5: challenger_sectors.add(sector_name)
        all_sectors_df.sort_values(by='inflow_delta', ascending=False, inplace=True)
        all_sectors_df['momentum_rank'] = range(1, len(all_sectors_df) + 1)

        # 4. 【V6.5 核心升级】动态计算“市场量能基准”
        positive_flow_df = all_sectors_df[all_sectors_df['今日主力净流入-净额'] > 0].copy()
        if positive_flow_df.empty:
            logging.info("今日无资金净流入的板块，强势板块分析结束。")
            STRONG_SECTORS_LIST, MAINLINE_SECTOR_LIST = [], []
            PREVIOUS_SECTOR_DATA = {}
            df_analysis_global = pd.DataFrame()
            return

        total_positive_inflow = positive_flow_df['今日主力净流入-净额'].sum()
        soul_tier_inflow = total_positive_inflow * 0.10
        core_tier_inflow = total_positive_inflow * 0.05
        active_tier_inflow = total_positive_inflow * 0.02
        logging.info(
            f"动态量能基准 -> 灵魂级: {format_amount(soul_tier_inflow)}, 核心级: {format_amount(core_tier_inflow)}, 活跃级: {format_amount(active_tier_inflow)}")

        # 5. 【V6.5 核心升级】判断“资金断层”
        is_gap_effect = False
        gap_leader_name = None
        if len(positive_flow_df) >= 2:
            top1_inflow = positive_flow_df.iloc[0]['今日主力净流入-净额']
            top2_inflow = positive_flow_df.iloc[1]['今日主力净流入-净额']
            # 使用固定的1.8倍作为断层判断标准
            if top2_inflow > 0 and (top1_inflow / top2_inflow) >= 1.8:
                is_gap_effect = True
                gap_leader_name = positive_flow_df.iloc[0]['名称']

        # 6. 【V6.5 核心升级】对所有板块进行多因子评分
        analysis_results = []
        for index, row in all_sectors_df.iterrows():
            sector_name = row['名称']
            inflow = row['今日主力净流入-净额']
            rank = row['rank']
            reasons = []
            total_score = 0

            # --- 新评分逻辑开始 ---
            strength_score = 0
            if is_gap_effect and sector_name == gap_leader_name:
                # 资金断层得分逻辑
                if inflow >= core_tier_inflow:
                    strength_score = 10;
                    reasons.append("主线真龙(10)")
                elif inflow >= active_tier_inflow:
                    strength_score = 5;
                    reasons.append("支线龙头(5)")
                else:  # inflow < active_tier_inflow
                    strength_score = 2;
                    reasons.append("局部偷袭(2)")
            else:
                # 市场容量 + 头部优势 得分逻辑
                capacity_score = 0
                if inflow >= soul_tier_inflow:
                    capacity_score = 5;
                    reasons.append("市场灵魂(5)")
                elif inflow >= core_tier_inflow:
                    capacity_score = 3;
                    reasons.append("核心战场(3)")
                elif inflow >= active_tier_inflow:
                    capacity_score = 1;
                    reasons.append("活跃板块(1)")

                head_position_score = 0
                # 只有在活跃级以上的板块，其头部排名才有意义
                if rank <= 3 and capacity_score > 0:
                    head_position_score = 2;
                    reasons.append("头部优势(2)")

                strength_score = capacity_score + head_position_score

            # 只有当板块至少达到活跃级或具有局部偷袭特征时，才计算其他分数
            if inflow >= active_tier_inflow or strength_score > 0:
                persistence_score = 0
                continuity_count = SECTOR_STRENGTH_TRACKER.get(sector_name, 0)
                if continuity_count >= MAINLINE_CONTINUITY_THRESHOLD:
                    persistence_score = 3; reasons.append("持续霸榜(3)")
                elif continuity_count >= 15:
                    persistence_score = 1; reasons.append("持续活跃(1)")

                momentum_score = 0
                momentum_rank = row['momentum_rank']
                if momentum_rank <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif momentum_rank <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")

                challenger_score = 0
                if sector_name in challenger_sectors: challenger_score = 3; reasons.append("主线挑战者(3)")

                total_score = strength_score + persistence_score + momentum_score + challenger_score
            else:
                # 对于量能不足且无断层的板块，只给一个基础的动量分
                momentum_score = 0
                momentum_rank = row['momentum_rank']
                if momentum_rank <= 3:
                    momentum_score = 4; reasons.append("动量先锋(4)")
                elif momentum_rank <= 10:
                    momentum_score = 2; reasons.append("动量跟进(2)")
                total_score = momentum_score

            # 只有得分大于0才记录
            if total_score > 0:
                # --- 【新增 V8.6】板块状态标签模块 ---
                sector_status = ""
                continuity_count = SECTOR_STRENGTH_TRACKER.get(sector_name, 0)
                challenger_score = 3 if sector_name in challenger_sectors else 0
                
                # 核心主线判断：得分高且稳定
                if total_score >= MAINLINE_SCORE_THRESHOLD and continuity_count > 1:
                    sector_status = "主线核心"
                # 新晋强势判断：首次冲入高分榜或身为挑战者
                elif (total_score >= POTENTIAL_SCORE_THRESHOLD and continuity_count <= 3) or (challenger_score > 0):
                    sector_status = "新晋强势"
                # 持续强势判断：长期保持高分
                elif total_score >= POTENTIAL_SCORE_THRESHOLD and continuity_count > 3:
                    sector_status = "持续强势"
                # --- 修改结束 ---
                
                analysis_results.append({
                    "板块名称": sector_name, "type": row['type'], "总分": total_score,
                    "状态": sector_status, # 新增状态列
                    "评分理由": ' + '.join(reasons) if reasons else "无",
                    "今日涨跌幅": row['今日涨跌幅'], "今日主力净流入-净额": inflow,
                    "持续强势次数": SECTOR_STRENGTH_TRACKER.get(sector_name, 0)
                })

        # 7. 将评分结果保存到全局 (逻辑不变)
        df_analysis_global = pd.DataFrame(analysis_results).sort_values(by="总分",
                                                                        ascending=False) if analysis_results else pd.DataFrame()

        # 8. 根据总分进行分类，并更新全局列表 (逻辑不变)
        if not df_analysis_global.empty:
            mainline_df = df_analysis_global[df_analysis_global['总分'] >= MAINLINE_SCORE_THRESHOLD]
            potential_df = df_analysis_global[
                (df_analysis_global['总分'] >= POTENTIAL_SCORE_THRESHOLD) & (
                            df_analysis_global['总分'] < MAINLINE_SCORE_THRESHOLD)]
            MAINLINE_SECTOR_LIST = mainline_df['板块名称'].tolist()
            potential_list = potential_df['板块名称'].tolist()
            strong_sectors_set = set(MAINLINE_SECTOR_LIST) | set(potential_list) | challenger_sectors
            STRONG_SECTORS_LIST = list(strong_sectors_set)
        else:
            STRONG_SECTORS_LIST, MAINLINE_SECTOR_LIST = [], []

        # 9. 更新持续性追踪器 (逻辑不变)
        current_strong_set = set(STRONG_SECTORS_LIST)
        for sector_name in current_strong_set:
            SECTOR_STRENGTH_TRACKER[sector_name] = SECTOR_STRENGTH_TRACKER.get(sector_name, 0) + 1
        for sector_name in list(SECTOR_STRENGTH_TRACKER.keys()):
            if sector_name not in current_strong_set:
                SECTOR_STRENGTH_TRACKER[sector_name] = 0

        # 10. 打印确认信息 (逻辑不变)
        print(
            f"--- 板块分析完成，识别到主线板块 {len(MAINLINE_SECTOR_LIST)} 个，潜在支线 {len(STRONG_SECTORS_LIST) - len(MAINLINE_SECTOR_LIST)} 个 ---")

        # 11. 更新上一分钟的状态数据 (逻辑不变)
        PREVIOUS_SECTOR_DATA.clear()
        for _, row in all_sectors_df.iterrows():
            PREVIOUS_SECTOR_DATA[row['名称']] = {'inflow': row['今日主力净流入-净额'], 'rank': row['rank']}
        logging.info(f"已更新 {len(PREVIOUS_SECTOR_DATA)} 个板块的状态数据用于下次对比。")

    except Exception as e:
        import traceback
        logging.error(f"分析强势板块失败: {e}\n{traceback.format_exc()}")
        STRONG_SECTORS_LIST = STRONG_SECTORS_LIST if STRONG_SECTORS_LIST else []
        MAINLINE_SECTOR_LIST = MAINLINE_SECTOR_LIST if MAINLINE_SECTOR_LIST else []

# --- 任务二：扫描全市场排行和热点 (可独立调度) ---
# --- 新增：缓存工具函数 ---
def _ensure_cache_dir():
    """确保缓存目录存在"""
    if not os.path.exists(BOARD_CACHE_DIR):
        os.makedirs(BOARD_CACHE_DIR)

def _get_cache_file_path(cache_name):
    """获取缓存文件路径"""
    _ensure_cache_dir()
    return os.path.join(BOARD_CACHE_DIR, f"{cache_name}.pkl")

def _is_cache_valid(cache_file_path):
    """检查缓存是否有效"""
    if not os.path.exists(cache_file_path):
        return False

    if BOARD_CACHE_EXPIRE_DAYS == 0:  # 永不过期
        return True

    # 检查缓存文件的修改时间
    cache_time = datetime.fromtimestamp(os.path.getmtime(cache_file_path))
    current_time = datetime.now()
    days_diff = (current_time - cache_time).days

    return days_diff < BOARD_CACHE_EXPIRE_DAYS

def _save_cache(data, cache_name):
    """保存数据到缓存"""
    if not ENABLE_BOARD_CACHE:
        return

    try:
        cache_file_path = _get_cache_file_path(cache_name)
        with open(cache_file_path, 'wb') as f:
            pickle.dump(data, f)
        logging.info(f"缓存已保存: {cache_file_path}")
    except Exception as e:
        logging.warning(f"保存缓存失败: {e}")

def _load_cache(cache_name):
    """从缓存加载数据"""
    if not ENABLE_BOARD_CACHE:
        return None

    try:
        cache_file_path = _get_cache_file_path(cache_name)
        if _is_cache_valid(cache_file_path):
            with open(cache_file_path, 'rb') as f:
                data = pickle.load(f)
            logging.info(f"缓存已加载: {cache_file_path}")
            return data
    except Exception as e:
        logging.warning(f"加载缓存失败: {e}")

    return None

def _get_stock_board_map():
    """
    【V2.8 缓存版】全面获取个股与板块的映射关系表。
    新增功能：
    1. 支持缓存机制，避免重复API调用
    2. 增加更多新能源汽车相关概念
    3. 支持强制更新缓存
    4. 确保重要股票的映射关系完整
    :return: 一个包含 '代码' 和 '概念名称' 两列的DataFrame。
    """
    # 检查缓存
    cache_name = "stock_board_mapping"
    if not UPDATE_BOARD_CACHE:
        cached_data = _load_cache(cache_name)
        if cached_data is not None:
            logging.info(f"从缓存加载股票-板块映射关系，共 {len(cached_data)} 条记录")
            return cached_data

    logging.info("开始获取股票-板块映射关系...")
    all_maps = []

    # 方案一：通过所有行业板块获取完整映射（使用正确的API）
    try:
        logging.info("正在获取所有行业板块名称...")
        industry_names_df = ak.stock_board_industry_name_em()
        if not industry_names_df.empty and '板块名称' in industry_names_df.columns:
            logging.info(f"成功获取 {len(industry_names_df)} 个行业板块")

            # 遍历每个行业获取成分股
            for _, row in industry_names_df.iterrows():
                industry_name = row['板块名称']
                try:
                    industry_stocks = ak.stock_board_industry_cons_em(symbol=industry_name)
                    if not industry_stocks.empty and '代码' in industry_stocks.columns:
                        industry_map_df = industry_stocks[['代码']].copy()
                        industry_map_df['概念名称'] = industry_name
                        industry_map_df['代码'] = industry_map_df['代码'].astype(str).str.zfill(6)
                        all_maps.append(industry_map_df)
                        logging.info(f"✅ {industry_name}: {len(industry_map_df)} 只股票")
                    else:
                        logging.warning(f"❌ {industry_name}: 返回空数据")
                except Exception as e:
                    logging.warning(f"❌ {industry_name}: {e}")
        else:
            logging.warning("获取行业板块名称失败")
    except Exception as e:
        logging.warning(f"获取行业板块名称失败: {e}")

    # 方案一补充：通过所有概念板块获取完整映射
    try:
        logging.info("正在获取所有概念板块名称...")
        concept_names_df = ak.stock_board_concept_name_em()
        if not concept_names_df.empty and '板块名称' in concept_names_df.columns:
            logging.info(f"成功获取 {len(concept_names_df)} 个概念板块")

            # 只处理重要的概念板块（避免过多API调用）
            important_concept_keywords = ['新能源', '汽车', '锂电', '充电', '电池', '人工智能', '5G', '芯片', '军工']
            filtered_concepts = []

            for _, row in concept_names_df.iterrows():
                concept_name = row['板块名称']
                if any(keyword in concept_name for keyword in important_concept_keywords):
                    filtered_concepts.append(concept_name)

            logging.info(f"筛选出 {len(filtered_concepts)} 个重要概念板块")

            # 遍历重要概念获取成分股
            for concept_name in filtered_concepts:
                try:
                    concept_stocks = ak.stock_board_concept_cons_em(symbol=concept_name)
                    if not concept_stocks.empty and '代码' in concept_stocks.columns:
                        concept_map_df = concept_stocks[['代码']].copy()
                        concept_map_df['概念名称'] = concept_name
                        concept_map_df['代码'] = concept_map_df['代码'].astype(str).str.zfill(6)
                        all_maps.append(concept_map_df)
                        logging.info(f"✅ {concept_name}: {len(concept_map_df)} 只股票")
                    else:
                        logging.warning(f"❌ {concept_name}: 返回空数据")
                except Exception as e:
                    logging.warning(f"❌ {concept_name}: {e}")
        else:
            logging.warning("获取概念板块名称失败")
    except Exception as e:
        logging.warning(f"获取概念板块名称失败: {e}")

    # 方案二：通过重要行业板块成分股反向获取映射（扩展版）
    important_industries = [
        # 传统行业
        '钢铁行业', '有色金属', '煤炭行业', '石油行业', '化工行业', '建筑材料', '建筑装饰',
        '房地产', '公用事业', '交通运输', '仓储物流',
        # 金融行业
        '银行', '证券', '保险', '多元金融',
        # 消费行业
        '白酒', '食品饮料', '纺织服装', '家用电器', '汽车整车', '汽车零部件',
        '商贸零售', '旅游酒店', '文化传媒',
        # 科技行业
        '医药生物', '电子信息', '计算机应用', '通信服务', '软件开发',
        '半导体', '光伏设备', '风电设备', '电力设备',
        # 新兴行业
        '新能源', '环保工程', '军工行业', '航空航天'
    ]
    for industry in important_industries:
        try:
            logging.info(f"正在获取{industry}成分股...")
            industry_stocks = ak.stock_board_industry_cons_em(symbol=industry)
            if not industry_stocks.empty and '代码' in industry_stocks.columns:
                industry_map_df = industry_stocks[['代码']].copy()
                industry_map_df['概念名称'] = industry
                industry_map_df['代码'] = industry_map_df['代码'].astype(str).str.zfill(6)
                all_maps.append(industry_map_df)
                logging.info(f"成功从{industry}获取 {len(industry_map_df)} 条映射。")
        except Exception as e:
            logging.warning(f"获取{industry}成分股失败: {e}")

    # 方案三：通过概念板块成分股反向获取映射（重点概念）
    important_concepts = [
        # 新能源汽车相关
        '新能源车', '新能源汽车', '新能源', '锂电池', '充电桩', '燃料电池',
        '汽车芯片', '华为汽车', '小米汽车', '动力电池回收', '固态电池',
        # 科技相关
        '人工智能', '5G概念', '芯片概念', '军工',
        # 市场相关
        '融资融券', '沪股通', '深股通', '创业板综'
    ]
    for concept in important_concepts:
        try:
            logging.info(f"正在获取{concept}成分股...")
            concept_stocks = ak.stock_board_concept_cons_em(symbol=concept)
            if not concept_stocks.empty and '代码' in concept_stocks.columns:
                concept_map_df = concept_stocks[['代码']].copy()
                concept_map_df['概念名称'] = concept
                concept_map_df['代码'] = concept_map_df['代码'].astype(str).str.zfill(6)
                all_maps.append(concept_map_df)
                logging.info(f"成功从{concept}获取 {len(concept_map_df)} 条映射。")
        except Exception as e:
            logging.warning(f"获取{concept}成分股失败: {e}")

    # 方案四：重要股票的静态映射（确保关键股票不会缺失）
    static_mapping = {
        # 银行股
        '000001': ['银行', '金融', '平安银行'],
        '600036': ['银行', '金融', '招商银行'],
        '600000': ['银行', '金融', '浦发银行'],
        '601398': ['银行', '金融', '工商银行'],
        '601939': ['银行', '金融', '建设银行'],

        # 房地产
        '000002': ['房地产', '地产', '万科A'],
        '000001': ['房地产', '地产', '万科A'],  # 万科A的正确代码是000002

        # 白酒/食品饮料
        '000858': ['白酒', '食品饮料', '五粮液'],
        '600519': ['白酒', '食品饮料', '贵州茅台'],
        '000568': ['白酒', '食品饮料', '泸州老窖'],

        # 钢铁（确保包钢股份被正确映射）
        '600010': ['钢铁行业', '钢铁', '包钢股份'],
        '600019': ['钢铁行业', '钢铁', '宝钢股份'],
        '000709': ['钢铁行业', '钢铁', '河钢股份'],
        '000932': ['钢铁行业', '钢铁', '华菱钢铁'],
        '600581': ['钢铁行业', '钢铁', '八一钢铁'],
        '601003': ['钢铁行业', '钢铁', '柳钢股份'],
    }

    # 转换静态映射为DataFrame格式
    static_data = []
    for code, concepts in static_mapping.items():
        for concept in concepts:
            static_data.append({'代码': code, '概念名称': concept})

    if static_data:
        static_df = pd.DataFrame(static_data)
        all_maps.append(static_df)
        logging.info(f"添加静态映射 {len(static_df)} 条记录。")

    if not all_maps:
        logging.error("未能从任何数据源获取板块映射关系，返回空DataFrame。")
        return pd.DataFrame(columns=['代码', '概念名称'])

    # 合并并去重
    combined_map_df = pd.concat(all_maps, ignore_index=True)
    combined_map_df.drop_duplicates(inplace=True)

    # 验证重要股票是否在映射中
    test_stocks = [
        ('000001', '平安银行'),
        ('000002', '万科A'),
        ('600036', '招商银行'),
        ('000858', '五粮液'),
        ('600519', '贵州茅台'),
        ('600010', '包钢股份')
    ]

    for code, name in test_stocks:
        stock_check = combined_map_df[combined_map_df['代码'] == code]
        if not stock_check.empty:
            concepts = stock_check['概念名称'].tolist()
            logging.info(f"✅ 验证成功：{name}({code})映射关系: {concepts[:3]}{'...' if len(concepts) > 3 else ''}")
        else:
            logging.warning(f"⚠️ {name}({code})未在映射关系中找到")

    logging.info(f"合并去重后，共获得 {len(combined_map_df)} 条有效的股票-板块映射关系。")

    # 保存到缓存
    _save_cache(combined_map_df, cache_name)

    return combined_map_df
# --- 任务二：扫描全市场排行和热点 (可独立调度) ---


def task_scan_market_for_signals():
    """
    (独立任务) 扫描全市场资金流排名，寻找买入和卖出信号，并追踪大单。
    【V6.2 最终对齐版】: 买入逻辑与 backtestv4.py 完全一致，只保留评分制，并只买入主线板块信号。
    """
    global is_market_scan_running, PREVIOUS_RANK_DATA, PREVIOUS_INFLOW_DATA, MAINLINE_SECTOR_LIST

    if is_market_scan_running:
        logging.warning("市场扫描任务仍在运行中，跳过本次调度。")
        print("市场扫描任务仍在运行中，跳过本次调度。")
        return

    # 检查是否在交易时间内
    if ENABLE_TRADING_TIME_CHECK and not is_trading_time():
        current_time = datetime.now().strftime('%H:%M:%S')
        msg = f"非交易时间（当前时间: {current_time}），跳过市场扫描任务。交易时间: 9:25-11:30, 13:00-15:00"
        logging.info(msg)
        print(msg)
        return

    task_analyze_strong_sectors()
    run_breakout_scan()  # 这个函数内部会打印报告，并更新全局的 BREAKOUT_STOCKS_LIST

    is_market_scan_running = True
    try:
        logging.info("--- [任务开始] 扫描全市场资金流向排名 (含排名异动监控) ---")
        print("\n--- [任务开始] 扫描全市场资金流向排名 (含排名异动监控) ---")
        stock_df = None
        big_deal_df = None
        records = []
        buy_stock_codes = []
        sell_stock_codes = []
        buy_details = []
        sell_details = []
        rank_change_details = []  # 用于收集排名异动详情
        big_deal_details = []  # 用于收集大单追踪详情
        acceleration_stocks_found = []

        processed_count = 0
        buy_signal_count = 0
        sell_signal_count = 0
        big_deal_count = 0

        # --- 【新增】用于记录本轮已处理的股票，防止重复分析 ---
        processed_codes = set()

        # --- 【核心修改】: 调用新的辅助函数获取映射关系表 ---
        stock_board_concept_name_ths_df = _get_stock_board_map()
        if stock_board_concept_name_ths_df.empty:
            logging.error("未能从任何数据源获取板块映射关系，板块加分功能将受限。")

        for attempt in range(1, 4):
            try:
                stock_df = ak.stock_individual_fund_flow_rank(indicator="今日")
                break
            except Exception as e:
                logging.warning(f"重试获取资金流向数据: 第{attempt}次，错误: {e}")
                if attempt == 3:
                    logging.error(f"获取资金流向数据失败: {e}")
                    send_bark_notification(f"股票监控脚本错误: 获取资金流向数据失败 {e}", stock_code="000000",
                                           signal_type="Error")
                    return
                time_module.sleep(5)

        if stock_df is not None and not stock_df.empty:
            # --- 新增开始：为当前数据帧添加排名列 ---
            stock_df['排名'] = range(1, len(stock_df) + 1)
            # --- 新增结束 ---

            stock_df = stock_df[stock_df['代码'].str[:2].isin(SCAN_STOCK_TYPES)]
            # 【新增】保存原始数据到以日期命名的文件夹
            if not stock_df.empty:
                date_folder = get_date_folder()
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                csv_file = os.path.join(date_folder, f'fund_flow_rank_{timestamp}.csv')
                try:
                    stock_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                    logging.info(f"成功保存资金流向数据到: {csv_file}")
                    print(f"成功保存资金流向数据到: {csv_file}")
                except Exception as e:
                    logging.error(f"保存资金流向数据到 {csv_file} 失败: {e}")
                    print(f"保存资金流向数据到 {csv_file} 失败: {e}")

            # ==============================================================================
            # --- 【最小化新增功能】: 资金加速度监控 ---
            # ==============================================================================
            logging.info("--- 开始检测资金加速度 ---")
            print("--- 开始检测资金加速度 ---")

            # 创建一个临时的当前数据字典，用于快速查找和更新
            current_inflow_data_for_update = {}
            acceleration_stocks_found = []
            acceleration_records_to_save = []

            # 我们需要扫描更深的数据来发现异动的股票
            scan_depth_for_acceleration = min(200, len(stock_df))

            for index, row in stock_df.head(scan_depth_for_acceleration).iterrows():
                try:
                    code = str(row['代码']).zfill(6)
                    current_main_inflow = convert_to_float(row['今日主力净流入-净额'])

                    # 存入本次扫描数据，用于稍后更新 PREVIOUS_INFLOW_DATA
                    current_inflow_data_for_update[code] = {
                        'main_inflow': current_main_inflow,
                        'timestamp': datetime.now()  # 记录当前时间用于冷却判断
                    }

                    # 只有当 PREVIOUS_INFLOW_DATA 非空时，才进行比较
                    if code in PREVIOUS_INFLOW_DATA:
                        previous_data = PREVIOUS_INFLOW_DATA[code]
                        previous_main_inflow = previous_data['main_inflow']

                        # 计算资金加速度（单分钟增量）
                        acceleration_amount = current_main_inflow - previous_main_inflow

                        # 检查是否超过阈值
                        if acceleration_amount >= ACCELERATION_THRESHOLD_AMOUNT:
                            # 检查推送冷却时间
                            last_push_time = TODAY_NOTIFICATION_CACHE.get((code, 'Acceleration'), {}).get('timestamp')
                            if last_push_time and (
                                    datetime.now() - last_push_time).total_seconds() < ACCELERATION_PUSH_COOLDOWN:
                                logging.info(f"资金加速度信号冷却中: {code}，跳过推送。")
                                continue

                            # 满足条件，准备推送
                            stock_name = row['名称']
                            price = convert_to_float(row['最新价'])
                            change_percent = convert_to_float(row['今日涨跌幅'])
                            # 【新增开始】收集详细数据用于保存
                            acceleration_records_to_save.append({
                                'Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'Stock_Code': code,
                                'Stock_Name': stock_name,
                                'Price': price,
                                'Change_Percent': change_percent,
                                'Acceleration_Amount': acceleration_amount,
                                'Current_Main_Inflow': current_main_inflow,
                                'Previous_Main_Inflow': previous_main_inflow
                            })
                            acceleration_stocks_found.append(
                                f"{code} ({stock_name}): [1分钟流入激增 {format_amount(acceleration_amount)}]")

                            message = (
                                f"🔥 资金加速度: {stock_name} ({code})\n"
                                f"价格: {price:.2f} | 涨跌幅: {change_percent:.2f}%\n"
                                f"🚨 1分钟内主力资金流入激增: {format_amount(acceleration_amount)}!\n"
                                f"当前主力净流入: {format_amount(current_main_inflow)}\n"
                                f"时间: {datetime.now().strftime('%H:%M:%S')}"
                            )

                            # 发送独立的“资金加速度”推送
                            send_bark_notification(message, stock_code=code, signal_type='Acceleration')
                            # 更新推送缓存，包含时间戳
                            TODAY_NOTIFICATION_CACHE[(code, 'Acceleration')] = {'timestamp': datetime.now()}
                            logging.info(f"已推送资金加速度信号: {code} ({stock_name})")
                            print(f"🔥 已推送资金加速度信号: {code} ({stock_name})")

                except Exception as e:
                    logging.error(f"处理股票 {row.get('代码', 'N/A')} 的资金加速度时出错: {e}")

            # 更新全局的上一分钟数据
            PREVIOUS_INFLOW_DATA = current_inflow_data_for_update
            logging.info(f"已更新 {len(PREVIOUS_INFLOW_DATA)} 条股票资金流数据，用于下次加速度计算。")

            if acceleration_stocks_found:
                print("\n--- 资金加速度信号详情 ---")
                for detail in acceleration_stocks_found:
                    print(detail)
            else:
                print("本次扫描未检测到资金加速度异动。")

            # 【新增开始】保存加速度信号到文件
            if acceleration_records_to_save:
                date_folder = get_date_folder()
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                acceleration_file = os.path.join(date_folder, f'acceleration_signals_{timestamp}.csv')
                try:
                    pd.DataFrame(acceleration_records_to_save).to_csv(acceleration_file, index=False,
                                                                      encoding='utf-8-sig')
                    logging.info(f"成功保存资金加速度信号数据到: {acceleration_file}")
                    print(f"成功保存资金加速度信号数据到: {acceleration_file}")
                except Exception as e:
                    logging.error(f"保存资金加速度信号数据到 {acceleration_file} 失败: {e}")
                    print(f"保存资金加速度信号数据到 {acceleration_file} 失败: {e}")
            # 【新增结束】

            # --- 新增开始：获取并保存其他资金流数据 ---
            date_folder = get_date_folder()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 2. 获取板块资金流排名（行业资金流）
            try:
                sector_fund_flow_df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")
                if not sector_fund_flow_df.empty:
                    sector_file = os.path.join(date_folder, f'sector_fund_flow_rank_{timestamp}.csv')
                    sector_fund_flow_df.to_csv(sector_file, index=False, encoding='utf-8-sig')
                    logging.info(f"成功保存板块资金流排名数据到: {sector_file}")
                    print(f"成功保存板块资金流排名数据到: {sector_file}")

                    # --- 【最小化新增功能】: 获取并保存领涨板块的个股资金流 ---
                    # 1. 提取排名前三的板块名称
                    top_sectors = sector_fund_flow_df.head(3)['名称'].tolist()
                    logging.info(f"识别到领涨板块前三名: {', '.join(top_sectors)}")
                    print(f"识别到领涨板块前三名: {', '.join(top_sectors)}")

                    # 2. 循环获取并保存个股资金流
                    for sector_name in top_sectors:
                        try:
                            logging.info(f"正在获取板块 '{sector_name}' 的个股资金流...")
                            # 调用新接口获取板块内个股数据
                            stock_summary_df = ak.stock_sector_fund_flow_summary(symbol=sector_name, indicator="今日")

                            if not stock_summary_df.empty:
                                # 3. 按照规则保存文件
                                # 清理文件名中的非法字符
                                safe_sector_name = re.sub(r'[\\/*?:"<>|]', "", sector_name)
                                summary_file = os.path.join(date_folder,
                                                            f'sector_summary_{safe_sector_name}_{timestamp}.csv')
                                stock_summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
                                logging.info(f"成功保存 '{sector_name}' 板块个股资金流数据到: {summary_file}")
                                print(f"成功保存 '{sector_name}' 板块个股资金流数据到: {summary_file}")
                            else:
                                logging.warning(f"获取到板块 '{sector_name}' 的个股资金流数据为空。")

                        except Exception as e_summary:
                            logging.error(f"获取板块 '{sector_name}' 的个股资金流失败: {e_summary}")
                            print(f"获取板块 '{sector_name}' 的个股资金流失败: {e_summary}")
                    # --- 【新增功能】结束 ---
            except Exception as e:
                logging.error(f"获取板块资金流排名数据失败: {e}")
                print(f"获取板块资金流排名数据失败: {e}")

            # 3. 获取主力净流入排名
            try:
                main_fund_flow_df = ak.stock_main_fund_flow(symbol="全部股票")
                if not main_fund_flow_df.empty:
                    main_file = os.path.join(date_folder, f'main_fund_flow_{timestamp}.csv')
                    main_fund_flow_df.to_csv(main_file, index=False, encoding='utf-8-sig')
                    logging.info(f"成功保存主力净流入排名数据到: {main_file}")
                    print(f"成功保存主力净流入排名数据到: {main_file}")
            except Exception as e:
                logging.error(f"获取主力净流入排名数据失败: {e}")
                print(f"获取主力净流入排名数据失败: {e}")

            # 4. 获取同花顺概念资金流
            try:
                concept_fund_flow_df = ak.stock_fund_flow_concept(symbol="即时")
                if not concept_fund_flow_df.empty:
                    concept_file = os.path.join(date_folder, f'concept_fund_flow_{timestamp}.csv')
                    concept_fund_flow_df.to_csv(concept_file, index=False, encoding='utf-8-sig')
                    logging.info(f"成功保存同花顺概念资金流数据到: {concept_file}")
                    print(f"成功保存同花顺概念资金流数据到: {concept_file}")
            except Exception as e:
                logging.error(f"获取同花顺概念资金流数据失败: {e}")
                print(f"获取同花顺概念资金流数据失败: {e}")
            # --- 新增结束：其他资金流数据获取和保存 ---

            # --- 新增开始：在处理数据前，先进行排名异动检测 ---
            # 创建一个临时的当前数据字典，用于快速查找
            current_rank_data = {}
            # 我们需要扫描全部（或前200名）的数据来发现从后往前冲的“黑马”
            scan_depth_for_rank_change = min(200, len(stock_df))  # 设定扫描深度
            for index, row in stock_df.head(scan_depth_for_rank_change).iterrows():
                code = str(row['代码']).zfill(6)
                current_rank_data[code] = {
                    'rank': row['排名'],
                    'main_ratio': convert_to_float(row['今日主力净流入-净占比']),
                    'full_row': row  # 保存整行数据以便推送时使用
                }

            # 只有当 PREVIOUS_RANK_DATA 非空时，才进行比较
            if PREVIOUS_RANK_DATA:
                logging.info("--- 开始检测排名和资金占比异动 ---")
                print("--- 开始检测排名和资金占比异动 ---")
                rank_change_records = []  # 用于收集排名异动数据
                for code, current_data in current_rank_data.items():
                    # 检查是否是“新面孔”或“排名大幅提升”
                    if code in PREVIOUS_RANK_DATA:
                        previous_data = PREVIOUS_RANK_DATA[code]

                        # 条件1: 排名从80名开外冲进前50名
                        rank_jump_condition = (previous_data['rank'] > 80 and current_data['rank'] <= 50)

                        # 条件2: 主力净占比从<5%飙升到10%以上
                        ratio_surge_condition = (previous_data['main_ratio'] < 5 and current_data['main_ratio'] >= 10)

                        # 调试信息：记录检测到的排名变化
                        if previous_data['rank'] != current_data['rank'] or abs(
                                previous_data['main_ratio'] - current_data['main_ratio']) > 5:
                            logging.debug(
                                f"检测到股票 {code} 变化: 排名 {previous_data['rank']}→{current_data['rank']}, 主力净占比 {previous_data['main_ratio']:.2f}%→{current_data['main_ratio']:.2f}%")

                        # 只要满足其中一个条件，就值得关注
                        if rank_jump_condition or ratio_surge_condition:
                            # 准备推送信息
                            row_data = current_data['full_row']
                            stock_name = row_data['名称']
                            stock_type = get_stock_type(code, stock_name)
                            price = convert_to_float(row_data['最新价'])
                            change_percent = convert_to_float(row_data['今日涨跌幅'])
                            main_net_abs = convert_to_float(row_data['今日主力净流入-净额'])

                            # 避免重复推送异动信号
                            # 使用特殊的 'RankChange' 信号类型
                            cache_key = (code, 'RankChange')
                            if cache_key in TODAY_NOTIFICATION_CACHE:
                                logging.info(f"跳过重复的排名异动推送: {code} ({stock_name})")
                                continue

                            # 收集排名异动数据用于保存到文件
                            rank_change_records.append({
                                'Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'Stock_Code': code,
                                'Stock_Name': stock_name,
                                'Stock_Type': stock_type,
                                'Price': price,
                                'Change_Percent': change_percent,
                                'Old_Rank': previous_data['rank'],
                                'New_Rank': current_data['rank'],
                                'Old_Main_Ratio': previous_data['main_ratio'],
                                'New_Main_Ratio': current_data['main_ratio'],
                                'Main_Net_Inflow': main_net_abs,
                                'Rank_Jump_Condition': rank_jump_condition,
                                'Ratio_Surge_Condition': ratio_surge_condition
                            })

                            message = RANK_CHANGE_TEMPLATE.format(
                                stock_name=stock_name,
                                stock_code=code,
                                stock_type=stock_type,
                                price=price,
                                change=change_percent,
                                old_rank=previous_data['rank'],
                                new_rank=current_data['rank'],
                                old_ratio=previous_data['main_ratio'],
                                new_ratio=current_data['main_ratio'],
                                main_flow=format_amount(main_net_abs),
                                time=datetime.now().strftime('%H:%M:%S')
                            )
                            # 发送独立的“排名异动”推送
                            send_bark_notification(message, stock_code=code, signal_type='RankChange')
                            logging.info(f"已推送排名异动信号: {code} ({stock_name})")
                            print(f"🚀 已推送排名异动信号: {code} ({stock_name})")

                            # 收集排名异动详情用于报告打印
                            rank_change_details.append(
                                f"{code} ({stock_name}): [排名: {previous_data['rank']}→{current_data['rank']}; 主力净占比: {previous_data['main_ratio']:.2f}%→{current_data['main_ratio']:.2f}%]")

                # 保存排名异动数据到文件
                if rank_change_records:
                    date_folder = get_date_folder()
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    rank_change_file = os.path.join(date_folder, f'rank_change_{timestamp}.csv')
                    try:
                        pd.DataFrame(rank_change_records).to_csv(rank_change_file, index=False, encoding='utf-8-sig')
                        logging.info(f"成功保存排名异动数据到: {rank_change_file}")
                        print(f"成功保存排名异动数据到: {rank_change_file}")
                    except Exception as e:
                        logging.error(f"保存排名异动数据到 {rank_change_file} 失败: {e}")
                        print(f"保存排名异动数据到 {rank_change_file} 失败: {e}")
                else:
                    logging.info("本次扫描未检测到符合条件的排名异动")
                    print("本次扫描未检测到符合条件的排名异动")
            else:
                logging.info("--- 首次运行，初始化排名数据，下次运行开始检测异动 ---")
                print("--- 首次运行，初始化排名数据，下次运行开始检测异动 ---")

            # 更新全局缓存为当前数据，为下一次比较做准备
            PREVIOUS_RANK_DATA = current_rank_data
            logging.info(f"已更新 {len(PREVIOUS_RANK_DATA)} 条股票状态到缓存，用于下次比较。")
            print(f"已更新 {len(PREVIOUS_RANK_DATA)} 条股票状态到缓存，用于下次比较。")
            # --- 新增结束 ---

            if not stock_df.empty:
                stock_df = stock_df[~stock_df['名称'].str.contains('ST|S*ST|SST|NST', na=False)]
                if not stock_df.empty:
                    # --- 【核心修改-1】: 将全市场Top20和强势板块龙头合并分析 ---
                    top_stocks_df = stock_df.head(20)
                    processed_count = len(top_stocks_df)
                    logging.info(f"仅处理资金排名前 {processed_count} 的股票")
                    print(f"\n--- 资金流向排行榜前20名 ---")
                    display_df = top_stocks_df[[
                        '代码', '名称', '今日主力净流入-净额', '今日主力净流入-净占比',
                        '今日超大单净流入-净额', '今日超大单净流入-净占比', '今日涨跌幅', '最新价'
                    ]].copy()
                    display_df['今日主力净流入-净额'] = display_df['今日主力净流入-净额'].apply(
                        lambda x: format_amount(float(x)) if pd.notna(x) else '0.00'
                    )
                    display_df['今日超大单净流入-净额'] = display_df['今日超大单净流入-净额'].apply(
                        lambda x: format_amount(float(x)) if pd.notna(x) else '0.00'
                    )
                    display_df['今日主力净流入-净占比'] = display_df['今日主力净流入-净占比'].apply(
                        lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%'
                    )
                    display_df['今日超大单净流入-净占比'] = display_df['今日超大单净流入-净占比'].apply(
                        lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%'
                    )
                    display_df['今日涨跌幅'] = display_df['今日涨跌幅'].apply(
                        lambda x: f"{float(x):.2f}%" if pd.notna(x) else '0.00%'
                    )
                    display_df['最新价'] = display_df['最新价'].apply(
                        lambda x: f"{float(x):.2f}" if pd.notna(x) else '0.00'
                    )
                    print(tabulate(display_df, headers='keys', tablefmt='psql', showindex=False))
                    print(f"--- 排行榜结束 ---\n")

                    # --- 【新增】将全市场Top20和强势板块龙头股合并到一个待处理列表 ---
                    all_stocks_to_process = [top_stocks_df]
                    strong_sector_leaders = {}  # 【新增】在这里初始化

                    if STRONG_SECTORS_LIST:
                        print("\n--- 开始扫描强势板块内的领涨股 ---")
                        for sector_name in STRONG_SECTORS_LIST:
                            try:
                                logging.info(f"正在获取强势板块 '{sector_name}' 的个股资金流...")
                                sector_leader_df = ak.stock_sector_fund_flow_summary(symbol=sector_name,
                                                                                     indicator="今日")
                                if not sector_leader_df.empty:
                                    # 只取板块内资金流入前3的龙头
                                    top_sector_stocks = sector_leader_df.head(3)
                                    all_stocks_to_process.append(top_sector_stocks)

                                    # 【新增】填充板块龙头信息
                                    top_3_codes = top_sector_stocks['代码'].astype(str).str.zfill(6).tolist()
                                    if top_3_codes:
                                        strong_sector_leaders[sector_name] = top_3_codes

                                    logging.info(
                                        f"板块 '{sector_name}' 添加 {len(top_sector_stocks)} 只龙头股到待分析列表。")
                                else:
                                    logging.warning(f"获取板块 '{sector_name}' 数据为空。")
                            except Exception as e_sector:
                                logging.error(f"获取强势板块 '{sector_name}' 内个股资金流失败: {e_sector}")

                    # 合并所有待处理的DataFrame，并对整个分析池进行处理
                    combined_df = pd.concat(all_stocks_to_process, ignore_index=True)
                    # --- 修改结束 ---

                    for _, row in combined_df.iterrows():
                        stock_code = str(row['代码']).zfill(6)

                        # --- 【新增】跳过已处理的股票，避免重复分析 ---
                        if stock_code in processed_codes:
                            continue

                        stock_name = row['名称']
                        stock_type = get_stock_type(stock_code, stock_name)
                        main_net_abs = convert_to_float(row['今日主力净流入-净额'])
                        main_net = main_net_abs / 10000
                        main_ratio = convert_to_float(row['今日主力净流入-净占比'])
                        super_net_abs = convert_to_float(row['今日超大单净流入-净额'])
                        super_net = super_net_abs / 10000
                        super_ratio = convert_to_float(row['今日超大单净流入-净占比'])
                        large_net_abs = convert_to_float(row['今日大单净流入-净额'])
                        large_net = large_net_abs / 10000
                        large_ratio = convert_to_float(row['今日大单净流入-净占比'])
                        medium_net_abs = convert_to_float(row['今日中单净流入-净额'])
                        medium_net = medium_net_abs / 10000
                        medium_ratio = convert_to_float(row['今日中单净流入-净占比'])
                        small_net_abs = convert_to_float(row['今日小单净流入-净额'])
                        small_net = small_net_abs / 10000
                        small_ratio = convert_to_float(row['今日小单净流入-净占比'])
                        price = convert_to_float(row['最新价'])
                        change_percent = convert_to_float(row['今日涨跌幅'])

                        try:
                            conn = sqlite3.connect('fund_flow.db')
                            historical_data = pd.read_sql_query(
                                f"SELECT * FROM daily_fund_flow WHERE stock_code='{stock_code}'", conn)
                            conn.close()
                        except Exception as e:
                            logging.error(f"获取 {stock_code} 历史数据失败: {e}")
                            historical_data = pd.DataFrame()

                        is_sell, reasons = evaluate_sell_condition(main_net, main_ratio, super_net, super_ratio,
                                                                   large_net, large_ratio,
                                                                   medium_net, medium_ratio, small_net, small_ratio,
                                                                   historical_data)
                        if is_sell:
                            sell_stock_codes.append(stock_code)
                            reason_str = "; ".join(reasons)
                            sell_details.append(f"{stock_code} ({stock_name}): [{reason_str}]")
                            message = SELL_TEMPLATE.format(
                                stock_name=stock_name, stock_code=stock_code, stock_type=stock_type, price=price,
                                change=change_percent, main_flow=format_amount(main_net_abs),
                                main_ratio=main_ratio,
                                super_flow=format_amount(super_net_abs), super_ratio=super_ratio,
                                big_flow=format_amount(large_net_abs), big_ratio=large_ratio,
                                mid_flow=format_amount(medium_net_abs), mid_ratio=medium_ratio,
                                small_flow=format_amount(small_net_abs), small_ratio=small_ratio,
                                hit_conditions=reason_str,
                                time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            )
                            if ENABLE_SELL_NOTIFICATION:
                                send_bark_notification(
                                    message, stock_code=stock_code, signal_type='Sell',
                                    main_flow=main_net, main_ratio=main_ratio
                                )
                            records.append({
                                'Time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'Signal': 'Sell',
                                'Stock_Code': stock_code,
                                'Stock_Name': stock_name, 'Stock_Type': stock_type, 'Price': price,
                                'Change_Percent': change_percent, 'Main_Flow': main_net, 'Main_Ratio': main_ratio,
                                'Super_Flow': super_net, 'Super_Ratio': super_ratio, 'Big_Flow': large_net,
                                'Big_Ratio': large_ratio, 'Mid_Flow': medium_net, 'Mid_Ratio': medium_ratio,
                                'Small_Flow': small_net, 'Small_Ratio': small_ratio
                            })

                        # --- 【逻辑对齐】开始：完全复制 backtestv4.py 的买入判断逻辑 ---
                        buy_score = 0
                        hit_score_reasons = []

                        # --- FIX START: 定义 related_sectors ---
                        # 获取股票所属板块概念
                        stock_concepts = []
                        if not stock_board_concept_name_ths_df.empty:
                            try:
                                stock_concepts = stock_board_concept_name_ths_df[
                                    stock_board_concept_name_ths_df['代码'] == stock_code
                                    ]['概念名称'].tolist()
                            except Exception as e:
                                logging.debug(f"为 {stock_code} 查找板块概念时出错: {e}")
                                pass  # 保持 stock_concepts 为空列表

                        # 判断股票是否属于当前已识别的强势板块或主线板块
                        related_sectors = [concept for concept in stock_concepts if concept in STRONG_SECTORS_LIST]
                        # --- FIX END ---

                        is_mainline = any(s in MAINLINE_SECTOR_LIST for s in related_sectors)
                        if is_mainline:
                            buy_score += BUY_CONDITIONS_SCORE['mainline_sector_bonus']
                            hit_score_reasons.append(f"主线板块({BUY_CONDITIONS_SCORE['mainline_sector_bonus']}分)")
                        elif related_sectors:
                            buy_score += BUY_CONDITIONS_SCORE['sector_bonus']
                            hit_score_reasons.append(f"强势板块({BUY_CONDITIONS_SCORE['sector_bonus']}分)")

                        if stock_code in BREAKOUT_STOCKS_LIST:
                            buy_score += BUY_CONDITIONS_SCORE['breakout_bonus']
                            hit_score_reasons.append(f"异动拉升({BUY_CONDITIONS_SCORE['breakout_bonus']}分)")

                        sector_rank_scored = False
                        for sector in related_sectors:
                            if sector in strong_sector_leaders and not sector_rank_scored:
                                leaders = strong_sector_leaders[sector]
                                if len(leaders) > 0 and stock_code == leaders[0]:
                                    buy_score += BUY_CONDITIONS_SCORE['sector_rank_1']
                                    hit_score_reasons.append(f"板块排名第1({BUY_CONDITIONS_SCORE['sector_rank_1']}分)")
                                    sector_rank_scored = True
                                elif len(leaders) > 1 and stock_code in leaders[1:3]:
                                    buy_score += BUY_CONDITIONS_SCORE['sector_rank_top_3']
                                    hit_score_reasons.append(
                                        f"板块排名前3({BUY_CONDITIONS_SCORE['sector_rank_top_3']}分)")
                                    sector_rank_scored = True

                        up_days_orig = historical_data[
                            historical_data['change_pct'] > 0] if not historical_data.empty else pd.DataFrame()
                        avg_up_main_net_orig = up_days_orig['main_net_inflow'].mean() if not up_days_orig.empty else 0

                        if (main_net_abs > 50000000 and main_ratio > 15):
                            buy_score += BUY_CONDITIONS_SCORE['fund_advantage']
                            hit_score_reasons.append(f"资金优势({BUY_CONDITIONS_SCORE['fund_advantage']}分)")
                        if (super_net_abs > 0 and medium_net_abs < 0 and small_net_abs < 0):
                            buy_score += BUY_CONDITIONS_SCORE['chip_concentration']
                            hit_score_reasons.append(f"筹码集中({BUY_CONDITIONS_SCORE['chip_concentration']}分)")
                        if (
                                avg_up_main_net_orig > 0 and main_net_abs > avg_up_main_net_orig * 3 and main_net_abs > 50000000):
                            buy_score += BUY_CONDITIONS_SCORE['history_anomaly']
                            hit_score_reasons.append(f"历史异动({BUY_CONDITIONS_SCORE['history_anomaly']}分)")

                        if 'price_breakout_strong' in BUY_CONDITIONS_SCORE and change_percent > 7:
                            buy_score += BUY_CONDITIONS_SCORE['price_breakout_strong']
                            hit_score_reasons.append(f"强势涨幅({BUY_CONDITIONS_SCORE['price_breakout_strong']}分)")
                        elif 'price_breakout_medium' in BUY_CONDITIONS_SCORE and change_percent > 5:
                            buy_score += BUY_CONDITIONS_SCORE['price_breakout_medium']
                            hit_score_reasons.append(f"中等涨幅({BUY_CONDITIONS_SCORE['price_breakout_medium']}分)")
                        elif 'price_breakout_weak' in BUY_CONDITIONS_SCORE and change_percent > 3:
                            buy_score += BUY_CONDITIONS_SCORE['price_breakout_weak']
                            hit_score_reasons.append(f"温和涨幅({BUY_CONDITIONS_SCORE['price_breakout_weak']}分)")

                        if buy_score >= BUY_SCORE_THRESHOLD:
                            # --- 【核心修改】只买入主线板块信号 ---
                            if '主线板块(10分)' in hit_score_reasons:
                                if stock_code not in FIRST_SIGNAL_TIMES:
                                    FIRST_SIGNAL_TIMES[stock_code] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                first_triggered_time = FIRST_SIGNAL_TIMES[stock_code]

                                if stock_code not in buy_stock_codes:
                                    buy_stock_codes.append(stock_code)

                                    score_reason = f"评分模式: {' + '.join(hit_score_reasons)} | 总分:{buy_score}"

                                    current_mainline_sectors = [s for s in related_sectors if s in MAINLINE_SECTOR_LIST]
                                    sector_text = f"【主线板块】: {', '.join(current_mainline_sectors)}"

                                    signal_source_tag = "【主线龙头】"
                                    detail_message = f"{stock_code} ({stock_name}): [{score_reason}] | {sector_text} - {first_triggered_time}"
                                    buy_details.append(detail_message)
                                    print(f"{signal_source_tag}: {detail_message}")

                                    message = BUY_TEMPLATE.format(
                                        stock_name=stock_name, stock_code=stock_code, stock_type=stock_type,
                                        price=price,
                                        change=change_percent, main_flow=format_amount(main_net_abs),
                                        main_ratio=main_ratio,
                                        super_flow=format_amount(super_net_abs), super_ratio=super_ratio,
                                        big_flow=format_amount(large_net_abs), big_ratio=large_ratio,
                                        mid_flow=format_amount(medium_net_abs), mid_ratio=medium_ratio,
                                        small_flow=format_amount(small_net_abs), small_ratio=small_ratio,
                                        time=first_triggered_time
                                    )
                                    if ENABLE_BUY_NOTIFICATION:
                                        send_bark_notification(message, stock_code=stock_code, signal_type='Buy',
                                                               main_flow=main_net, main_ratio=main_ratio)

                                    records.append({
                                        'Time': first_triggered_time, 'Signal': 'Buy', 'Stock_Code': stock_code,
                                        'Stock_Name': stock_name, 'Stock_Type': stock_type, 'Price': price,
                                        'Change_Percent': change_percent, 'Main_Flow': main_net,
                                        'Main_Ratio': main_ratio,
                                        'Super_Flow': super_net, 'Super_Ratio': super_ratio, 'Big_Flow': large_net,
                                        'Big_Ratio': large_ratio, 'Mid_Flow': medium_net, 'Mid_Ratio': medium_ratio,
                                        'Small_Flow': small_net, 'Small_Ratio': small_ratio
                                    })
                                    if PRINT_HISTORY:
                                        print_historical_data(stock_code)
                            # --- 【核心修改】结束 ---
                        else:
                            if stock_code in FIRST_SIGNAL_TIMES:
                                del FIRST_SIGNAL_TIMES[stock_code]
                                logging.info(f"龙头信号消失，已从缓存中移除: {stock_code}")
                        # --- 【逻辑对齐】结束 ---

                        processed_codes.add(stock_code)

        buy_signal_count = len(buy_stock_codes)
        sell_signal_count = len(sell_stock_codes)

        if buy_stock_codes:
            write_signal_file_atomically('D:/flow_buy.ebk', buy_stock_codes, overwrite=True)
        else:
            write_signal_file_atomically('D:/flow_buy.ebk', [], overwrite=True)

        if sell_stock_codes:
            write_signal_file_atomically('D:/flow_sell.ebk', sell_stock_codes, overwrite=False)

        big_deal_df = None
        for attempt in range(1, 4):
            try:
                big_deal_df = ak.stock_fund_flow_big_deal()
                break
            except Exception as e:
                logging.warning(f"重试获取大单追踪数据: 第{attempt}次，错误: {e}")
                if attempt == 3:
                    logging.error(f"获取大单追踪数据失败: {e}")
                    send_bark_notification(f"股票监控脚本错误: 获取大单追踪数据失败 {e}", stock_code="000000",
                                           signal_type="Error")
                time_module.sleep(5)

        if big_deal_df is not None and not big_deal_df.empty:
            big_deal_df['涨跌幅'] = big_deal_df['涨跌幅'].apply(convert_to_float)
            big_deal_df['成交额'] = big_deal_df['成交额'].apply(convert_to_float)
            big_deal_df['成交价格'] = big_deal_df['成交价格'].apply(convert_to_float)
            big_deal_df['涨跌额'] = big_deal_df['涨跌额'].apply(convert_to_float)
            big_deal_df['股票代码'] = big_deal_df['股票代码'].astype(str).str.zfill(6)
            big_deal_signals = big_deal_df[(big_deal_df['成交额'] > 2000) & (big_deal_df['涨跌幅'] > 0)]
            big_deal_signals = big_deal_signals.sort_values('成交时间', ascending=False).head(10)
            big_deal_count = len(big_deal_signals)
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            for _, row in big_deal_signals.iterrows():
                stock_type = get_stock_type(row['股票代码'], row['股票简称'])
                message = BIG_DEAL_TEMPLATE.format(
                    stock_name=row['股票简称'], stock_code=row['股票代码'], stock_type=stock_type,
                    deal_time=row['成交时间'], price=row['成交价格'], volume=int(row['成交量']),
                    amount=row['成交额'], deal_type=row['大单性质'], change=row['涨跌幅'],
                    change_amount=row['涨跌额'], time=current_time
                )
                if ENABLE_BUY_NOTIFICATION:
                    send_bark_notification(message, stock_code=row['股票代码'], signal_type='BigDeal')

                big_deal_details.append(
                    f"{row['股票代码']} ({row['股票简称']}): [成交额: {row['成交额']:.2f}万; 涨跌幅: {row['涨跌幅']:.2f}%; 大单性质: {row['大单性质']}]")
            if not big_deal_signals.empty:
                logging.info(f"推送 {len(big_deal_signals)} 条大单追踪信号")
                print(f"推送 {len(big_deal_signals)} 条大单追踪信号")

                date_folder = get_date_folder()
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                big_deal_file = os.path.join(date_folder, f'big_deal_{timestamp}.csv')
                try:
                    big_deal_signals.to_csv(big_deal_file, index=False, encoding='utf-8-sig')
                    logging.info(f"成功保存大单追踪数据到: {big_deal_file}")
                    print(f"成功保存大单追踪数据到: {big_deal_file}")
                except Exception as e:
                    logging.error(f"保存大单追踪数据到 {big_deal_file} 失败: {e}")
                    print(f"保存大单追踪数据到 {big_deal_file} 失败: {e}")

        if records:
            pd.DataFrame(records).to_csv(RECORD_FILE, mode='a', header=False, index=False, encoding='utf-8-sig')
            msg = f"已记录 {len(records)} 条信号到 {RECORD_FILE}"
            logging.info(msg)
            print(f"\n{msg}")


        # 【核心修改】调用新的分层报告函数
        global df_analysis_global
        print("\n" + "=" * 20 + " 市场结构分析报告 " + "=" * 20)
        master_report = generate_master_analysis_report(df_analysis_global, MAINLINE_SCORE_THRESHOLD, POTENTIAL_SCORE_THRESHOLD)
        print(master_report)
        logging.info("市场结构分析报告:\n" + master_report)


        report_msg = (
            f"报告摘要: 共扫描 {len(processed_codes)} 个股票 | "
            f"买入信号 {buy_signal_count} 个 | "
            f"卖出信号 {sell_signal_count} 个 | "
            f"大单追踪 {big_deal_count} 条。"
        )
        print(report_msg)
        logging.info(report_msg)

        print("\n" + "=" * 20 + " 强势板块报告 " + "=" * 20)
        rank_threshold = 10
        change_pct_threshold = 1.0
        print(f"--- (判断标准: 资金排名前 {rank_threshold} AND 板块涨幅 > {change_pct_threshold}%) ---")
        if STRONG_SECTORS_LIST:
            print(f"当前强势板块: {', '.join(STRONG_SECTORS_LIST)}")
            logging.info(f"当前强势板块: {', '.join(STRONG_SECTORS_LIST)}")
        else:
            print("暂无强势板块数据。")
            logging.info("暂无强势板块数据。")

        if buy_details:
            print("\n" + "--- 龙头信号详情 ---")
            for detail in buy_details:
                print(detail)

        if acceleration_records_to_save:
            print("\n" + "=" * 20 + " 资金加速度排行榜 " + "=" * 20)
            print("--- (监控1分钟内主力资金流入的剧烈变化) ---")
            table_data = []
            headers = ['代码', '名称', '当前排名', '涨跌幅', '激增金额', '激增前流入', '当前总流入']
            for record in acceleration_records_to_save:
                stock_code = record['Stock_Code']
                stock_info_row = stock_df[stock_df['代码'] == stock_code]
                if not stock_info_row.empty:
                    stock_info = stock_info_row.iloc[0]
                    table_data.append([
                        stock_code,
                        record['Stock_Name'],
                        stock_info['排名'],
                        f"{record['Change_Percent']:.2f}%",
                        format_amount(record['Acceleration_Amount']),
                        format_amount(record['Previous_Main_Inflow']),
                        format_amount(record['Current_Main_Inflow'])
                    ])
            if table_data:
                print(tabulate(table_data, headers=headers, tablefmt='psql'))

        if rank_change_details:
            print("\n" + "=" * 20 + " 排名异动排行榜 " + "=" * 20)
            print("--- (监控排名和占比的跃升) ---")
            for detail in rank_change_details:
                print(detail)

        if big_deal_details:
            print("\n" + "--- 大单追踪详情 ---")
            for detail in big_deal_details:
                print(detail)

        if sell_details:
            print("\n" + "--- 卖出信号详情 ---")
            for detail in sell_details:
                print(detail)

        print("\n" + "=" * 20 + " 异动拉升排行榜 " + "=" * 20)
        print("--- (监控从潜伏到爆发的股票) ---")
        try:
            today_str = datetime.now().strftime('%Y%m%d')
            target_dir = os.path.join('data', today_str)
            all_day_df = load_and_combine_data_scan(target_dir)

            if all_day_df is not None and not all_day_df.empty:
                current_time_for_scan = datetime.now().time()
                if current_time_for_scan <= AM_END_TIME:
                    period_name = "上午盘"
                    df_period = all_day_df
                else:
                    period_name = "下午盘"
                    df_period = all_day_df[all_day_df['timestamp'] >= PM_START_TIME]

                if not df_period.empty:
                    breakouts, _ = find_breakouts(df_period, period_name, all_day_df)

                    if breakouts:
                        sorted_breakouts = sorted(breakouts.items(),
                                                  key=lambda x: get_trend_slope_scan(x[1]['trajectory']['排名'],
                                                                                     x[1]['trajectory']['timestamp']))
                        table_data = []
                        headers = ['代码', '名称', '排名变化', '当前涨幅', '当前净流入', '排名趋势/分', '所属板块']
                        for code, data in sorted_breakouts:
                            traj = data['trajectory']
                            initial_rank = data['initial_rank']
                            if len(traj) > 0:
                                last = traj.iloc[-1]
                                rank_trend = get_trend_slope_scan(traj['排名'], traj['timestamp'])
                                stock_concepts = []
                                if not stock_board_concept_name_ths_df.empty:
                                    try:
                                        stock_concepts = stock_board_concept_name_ths_df[
                                            stock_board_concept_name_ths_df['代码'] == code]['概念名称'].tolist()
                                    except:
                                        pass
                                related_sectors = [concept for concept in stock_concepts if
                                                   concept in STRONG_SECTORS_LIST]
                                sector_text = ', '.join(related_sectors) if related_sectors else '无强势板块'
                                table_data.append([
                                    last['代码'],
                                    last['名称'],
                                    f"{int(initial_rank)} → {int(last['排名'])}",
                                    f"{last['今日涨跌幅']:.2f}%",
                                    format_currency_scan(last['今日主力净流入-净额']),
                                    f"{rank_trend:.2f}",
                                    sector_text
                                ])
                        print(tabulate(table_data, headers=headers, tablefmt='psql'))
                    else:
                        print("暂未发现符合条件的异动拉升股票。")
        except Exception as e:
            logging.error(f"整合异动拉升扫描报告时发生错误: {e}")
            print(f"整合异动拉升扫描报告时发生错误: {e}")

        print("=" * 58)

    except Exception as e:
        import traceback
        logging.error(f"市场扫描任务发生错误: {e}\n{traceback.format_exc()}")
        msg = f"市场扫描任务发生错误: {e}"
        logging.error(msg)
        print(msg)
        send_bark_notification(f"股票监控脚本错误: {str(e)}", stock_code="000000", signal_type="Error")
    finally:
        is_market_scan_running = False
        logging.info("--- [任务结束] 市场扫描完成 (状态锁已释放) ---")


# --- 新增：用于在后台线程中运行任务的辅助函数 ---
def run_threaded(job_func):
    job_thread = threading.Thread(target=job_func)
    job_thread.start()


def schedule_jobs():
    """【修改】设置独立的定时任务，并在后台线程中运行"""
    # 任务一：每分钟扫描市场排行
    # 注意：现在 task_scan_market_for_signals 内部会先调用板块和异动分析
    schedule.every(1).minutes.do(run_threaded, task_scan_market_for_signals)
    # 任务二：每2分钟检查一次持仓股
    schedule.every(2).minutes.do(run_threaded, task_scan_positions_for_sell_signals)
    # 任务三：每日15:05获取大盘资金流数据（收盘后）
    schedule.every().day.at("15:05").do(run_threaded, task_get_market_fund_flow)

    # --- 新增任务：每日15:06生成买入信号汇总报告 ---
    schedule.every().day.at("15:06").do(run_threaded, task_generate_daily_buy_summary)

    print("已设置定时任务：每1分钟扫描市场(后台)，每2分钟检查持仓(后台)，每日15:06生成汇总报告(后台)。")
    logging.info("已设置定时任务：每1分钟扫描市场(后台)，每2分钟检查持仓(后台)，每日15:06生成汇总报告(后台)。")

def schedule_jobs():
    """【修改】设置独立的定时任务，并在后台线程中运行"""
    # 任务一：每分钟扫描市场排行
    # 注意：现在 task_scan_market_for_signals 内部会先调用板块和异动分析
    schedule.every(1).minutes.do(run_threaded, task_scan_market_for_signals)
    # 任务二：每2分钟检查一次持仓股
    schedule.every(2).minutes.do(run_threaded, task_scan_positions_for_sell_signals)
    # 任务三：每日15:05获取大盘资金流数据（收盘后）
    schedule.every().day.at("15:05").do(run_threaded, task_get_market_fund_flow)

    # --- 新增任务：每日15:06生成买入信号汇总报告 ---
    schedule.every().day.at("15:06").do(run_threaded, task_generate_daily_buy_summary)

    print("已设置定时任务：每1分钟扫描市场(后台)，每2分钟检查持仓(后台)，每日15:06生成汇总报告(后台)。")
    logging.info("已设置定时任务：每1分钟扫描市场(后台)，每2分钟检查持仓(后台)，每日15:06生成汇总报告(后台)。")

if __name__ == "__main__":
    logging.info("启动股票资金流向监控脚本...")
    print("启动股票资金流向监控脚本...")

    # --- 新增：启动时检查并更新板块映射缓存 ---
    if UPDATE_BOARD_CACHE:
        print("检测到缓存更新标志，正在更新板块映射缓存...")
        logging.info("检测到缓存更新标志，正在更新板块映射缓存...")
        try:
            start_time = datetime.now()
            stock_board_map_df = _get_stock_board_map()
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            if not stock_board_map_df.empty:
                print(f"✅ 板块映射缓存更新成功！共 {len(stock_board_map_df)} 条记录，耗时 {duration:.2f} 秒")
                logging.info(f"板块映射缓存更新成功！共 {len(stock_board_map_df)} 条记录，耗时 {duration:.2f} 秒")
            else:
                print("⚠️ 板块映射缓存更新完成，但数据为空")
                logging.warning("板块映射缓存更新完成，但数据为空")
        except Exception as e:
            print(f"❌ 板块映射缓存更新失败: {e}")
            logging.error(f"板块映射缓存更新失败: {e}")
    else:
        # 检查缓存是否存在
        cache_file_path = os.path.join(BOARD_CACHE_DIR, "stock_board_mapping.pkl")
        if os.path.exists(cache_file_path):
            file_time = datetime.fromtimestamp(os.path.getmtime(cache_file_path))
            print(f"📁 使用现有板块映射缓存 (更新时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print("📁 板块映射缓存不存在，将在首次使用时创建")

    try:
        if os.path.exists(NOTIFICATION_FILE) and os.path.getsize(NOTIFICATION_FILE) > 0:
            history_df = pd.read_csv(NOTIFICATION_FILE, encoding='utf-8-sig', dtype={'Stock_Code': str})
            if 'Main_Ratio' not in history_df.columns:
                logging.warning("旧版 notification 文件，缺少 'Main_Ratio' 列，将使用默认值 0.0。")
                print("警告：检测到旧版 notification 文件，缺少 'Main_Ratio' 列。")
                history_df['Main_Ratio'] = 0.0
            today_str = datetime.now().strftime('%Y-%m-%d')
            today_history_df = history_df[
                history_df['Time'].str.startswith(today_str) & pd.notna(history_df['Main_Ratio'])]
            if not today_history_df.empty:
                print(f"检测到 {len(today_history_df)} 条有效的今日已推送历史，正在加载到缓存...")
                for _, row in today_history_df.iterrows():
                    formatted_code = str(row['Stock_Code']).zfill(6)
                    cache_key = (formatted_code, row['Signal_Type'])
                    TODAY_NOTIFICATION_CACHE[cache_key] = {'main_ratio': row['Main_Ratio']}
                print("缓存加载完成。")
    except Exception as e:
        logging.error(f"启动时加载推送历史失败: {e}")
        print(f"启动时加载推送历史失败: {e}")


    # 【核心修复】恢复 _bak.py 的逻辑：总是设置定时任务
    schedule_jobs()

    # 【核心修复】恢复 _bak.py 的逻辑：根据时间决定是否立即触发后台任务
    # 场景一：在交易时间内或DEBUG模式下启动，立即执行盘中扫描
    if DEBUG_MODE or (is_trading_day() and is_trading_time()):
        print("首次启动，立即在后台执行所有检查任务...")
        # 直接调用 run_threaded 来确保它们在后台运行，不阻塞主程序
        run_threaded(task_scan_positions_for_sell_signals)
        run_threaded(task_scan_market_for_signals)
    else:
        # 场景二：在非交易时间启动，检查是否需要补生成收盘报告
        is_after_market_close = datetime.now().time() >= time(15, 0)
        date_folder = get_date_folder()
        summary_file_path = os.path.join(date_folder, f'daily_buy_summary_{datetime.now().strftime("%Y%m%d")}.txt')

        if is_trading_day() and is_after_market_close and not os.path.exists(summary_file_path):
            print("收盘后启动，且当日汇总报告不存在，立即在后台生成...")
            logging.info("收盘后启动，且当日汇总报告不存在，立即在后台生成...")
            run_threaded(task_generate_daily_buy_summary)
        else:
            # 场景三：其他时间（如凌晨、非交易日），不执行任何初始任务
            msg = f"非交易日或非交易时间，跳过初始检查。"
            logging.info(msg)
            print(msg)

    # 【核心修复】恢复 _bak.py 的逻辑：总是进入无限循环以保证 schedule 持续工作
    while True:
        try:
            schedule.run_pending()
            time_module.sleep(1)
        except KeyboardInterrupt:
            print("\n检测到手动中断，正在退出脚本...")
            logging.info("脚本被手动中断，正在退出。")
            break