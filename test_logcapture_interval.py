#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LogCapture类的间隔输出功能
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块
import dynamic_gap_detector as dgd

def test_logcapture_interval():
    """测试LogCapture类的间隔输出功能"""
    print("=== 测试LogCapture类的间隔输出功能 ===")
    
    # 确保log目录存在
    log_dir = 'log'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 重置全局变量
    dgd._last_log_output_time = None
    
    # 测试日志文件路径
    test_date = "20250802"
    main_log_path = dgd.get_log_file_path("test_logcapture", test_date)
    
    print(f"主日志文件: {main_log_path}")
    
    # 清理旧的测试文件
    if os.path.exists(main_log_path):
        os.remove(main_log_path)
        print(f"清理旧文件: {main_log_path}")
    
    # 测试时间序列和对应的日志内容
    test_data = [
        ("09:30:00", "第一条日志 - 应该写入"),
        ("09:32:00", "第二条日志 - 不应该写入"),
        ("09:35:00", "第三条日志 - 应该写入"),
        ("09:37:00", "第四条日志 - 不应该写入"),
        ("09:40:00", "第五条日志 - 应该写入"),
    ]
    
    written_count = 0
    skipped_count = 0
    
    # 使用LogCapture进行测试
    with dgd.LogCapture(main_log_path, test_date) as log_capture:
        for time_str, content in test_data:
            # 更新当前时间
            current_time = datetime.strptime(time_str, '%H:%M:%S').time()
            log_capture.update_hourly_log(current_time)
            
            # 记录写入前的文件大小
            main_size_before = os.path.getsize(main_log_path) if os.path.exists(main_log_path) else 0
            
            # 通过print输出（会被LogCapture捕获）
            print(f"[{time_str}] {content}")
            
            # 检查文件大小是否变化
            main_size_after = os.path.getsize(main_log_path) if os.path.exists(main_log_path) else 0
            
            if main_size_after > main_size_before:
                print(f"✅ {time_str}: 日志已写入文件")
                written_count += 1
            else:
                print(f"⏭️  {time_str}: 日志已跳过文件写入")
                skipped_count += 1
    
    print(f"\n写入统计: 写入 {written_count} 条，跳过 {skipped_count} 条")
    
    # 显示最终的日志文件内容
    if os.path.exists(main_log_path):
        print(f"\n主日志文件内容:")
        with open(main_log_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    
    return written_count, skipped_count

def test_logcapture_disabled():
    """测试关闭间隔输出功能时的LogCapture行为"""
    print("\n=== 测试关闭间隔输出功能时的LogCapture行为 ===")
    
    # 临时关闭间隔输出功能
    original_setting = dgd.INTERVAL_LOG_OUTPUT
    dgd.INTERVAL_LOG_OUTPUT = False
    
    # 重置全局变量
    dgd._last_log_output_time = None
    
    # 确保log目录存在
    log_dir = 'log'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 测试日志文件路径
    test_date = "20250802"
    main_log_path = dgd.get_log_file_path("test_logcapture_disabled", test_date)
    
    print(f"主日志文件: {main_log_path}")
    
    # 清理旧的测试文件
    if os.path.exists(main_log_path):
        os.remove(main_log_path)
        print(f"清理旧文件: {main_log_path}")
    
    # 测试时间序列
    test_data = [
        ("09:30:00", "第一条日志"),
        ("09:32:00", "第二条日志"),
        ("09:35:00", "第三条日志"),
    ]
    
    written_count = 0
    
    # 使用LogCapture进行测试
    with dgd.LogCapture(main_log_path, test_date) as log_capture:
        for time_str, content in test_data:
            # 更新当前时间
            current_time = datetime.strptime(time_str, '%H:%M:%S').time()
            log_capture.update_hourly_log(current_time)
            
            # 记录写入前的文件大小
            main_size_before = os.path.getsize(main_log_path) if os.path.exists(main_log_path) else 0
            
            # 通过print输出（会被LogCapture捕获）
            print(f"[{time_str}] {content}")
            
            # 检查文件大小是否变化
            main_size_after = os.path.getsize(main_log_path) if os.path.exists(main_log_path) else 0
            
            if main_size_after > main_size_before:
                written_count += 1
    
    # 恢复原设置
    dgd.INTERVAL_LOG_OUTPUT = original_setting
    
    print(f"关闭间隔功能后，写入 {written_count} 条日志")
    
    # 显示最终的日志文件内容
    if os.path.exists(main_log_path):
        print(f"\n主日志文件内容:")
        with open(main_log_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    
    return written_count

def main():
    """主测试函数"""
    print("开始测试LogCapture类的间隔输出功能...")
    
    # 测试1: 间隔输出功能
    written, skipped = test_logcapture_interval()
    
    # 测试2: 关闭功能测试
    disabled_written = test_logcapture_disabled()
    
    print("\n=== 测试结果总结 ===")
    
    # 检查写入是否符合预期
    expected_written = 3  # 应该写入3条（09:30, 09:35, 09:40）
    expected_skipped = 2  # 应该跳过2条（09:32, 09:37）
    interval_correct = (written == expected_written and skipped == expected_skipped)
    print(f"✅ 间隔输出测试: {'正确' if interval_correct else '错误'} (写入{written}条，跳过{skipped}条)")
    
    # 检查关闭功能是否正确
    expected_disabled_written = 3  # 关闭功能后应该写入所有3条
    disabled_correct = (disabled_written == expected_disabled_written)
    print(f"✅ 关闭功能测试: {'正确' if disabled_correct else '错误'} (写入{disabled_written}条)")
    
    if interval_correct and disabled_correct:
        print("\n🎉 LogCapture间隔输出功能测试通过！")
        print("📝 功能说明:")
        print("   - LogCapture类现在支持间隔输出控制")
        print("   - 控制台输出不受影响，文件输出按间隔控制")
        print("   - 按小时分割的日志文件也受间隔控制影响")
    else:
        print("\n❌ LogCapture间隔输出功能测试失败，请检查代码实现。")

if __name__ == "__main__":
    main()
