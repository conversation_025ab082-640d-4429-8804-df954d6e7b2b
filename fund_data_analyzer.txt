根据您的 fund_data_analyzer.py 代码分析，我来详细列出所有读取的文件类型和判断规则：
1. 所有读取的文件类型
股票池相关

涨停股池 (zt_pool)
炸板股池 (zb_pool)

资金流相关

个股资金流 (fund_flow)
概念资金流 (concept_flow)
行业资金流 (sector_flow)
市场资金流 (market_flow)
主力资金流 (main_fund_flow)
个股资金流排名 (individual_flow)
资金流排名 (fund_flow_rank)

异动和信号

异动股票 (movers)
加速信号 (acceleration_signals)
股票信号 (stock_signals)
股票通知 (stock_notifications)

交易数据

大宗交易 (big_deal)
龙虎榜 (lhb)
大宗交易明细 (dzjy)

板块和指数

板块变化 (board_changes)
行业板块 (industry_board)
行业板块汇总 (sector_summary)
指数数据 (index)

其他数据

新闻 (news)
技术指标 (indicator)
公告 (notices)

2. 文件类型判断规则
python# 在 file_types 字典中定义的匹配模式
self.file_types = {
    # 个股资金流
    'fund_flow': [
        'fund_flow_tpdog.csv', 
        'ths_fund_flow.csv', 
        'fund_flow_akshare.csv'
    ],
    
    # 概念资金流
    'concept_flow': [
        'concept_fund_flow_tpdog.csv', 
        'concept_fund_flow_akshare.csv', 
        'concept_fund_flow_'  # 前缀匹配
    ],
    
    # 行业资金流
    'sector_flow': [
        'sector_fund_flow_tpdog.csv', 
        'sector_fund_flow_akshare.csv', 
        'sector_fund_flow_rank_'  # 前缀匹配
    ],
    
    # 涨停股池
    'zt_pool': [
        'zt_pool.csv', 
        'previous_zt_pool.csv', 
        '涨停股池_akshare_东方财富_',  # 前缀匹配
        '涨停股池_tpdog_'  # 前缀匹配
    ],
    
    # 炸板股池
    'zb_pool': [
        '炸板股池_akshare_东方财富_'  # 前缀匹配
    ],
    
    # 新闻
    'news': [
        'news_cls.csv', 
        'news_em.csv', 
        'news_ths.csv'
    ],
    
    # 指数
    'index': [
        'index_sh000001.csv', 
        'index_sz399006.csv'
    ],
    
    # 异动股票
    'movers': [
        'movers_大笔买入.csv', 
        'movers_有大买盘.csv'
    ],
    
    # 大宗交易
    'big_deal': [
        'ths_big_deal.csv', 
        'big_deal_'  # 前缀匹配
    ],
    
    # 板块变化
    'board_changes': [
        'board_changes.csv'
    ],
    
    # 市场资金流
    'market_flow': [
        'market_fund_flow.csv', 
        'market_fund_flow_'  # 前缀匹配
    ],
    
    # 行业板块
    'industry_board': [
        'industry_board_ths.csv', 
        'industry_board_em.csv', 
        'industry_board_akshare.csv'
    ],
    
    # 技术指标
    'indicator': [
        'indicator_创月新高.csv'
    ],
    
    # 个股资金流排名
    'individual_flow': [
        'individual_fund_flow_'  # 前缀匹配
    ],
    
    # 龙虎榜
    'lhb': [
        'lhb_jgmmtj.csv'
    ],
    
    # 大宗交易明细
    'dzjy': [
        'dzjy_mrmx.csv'
    ],
    
    # 公告
    'notices': [
        'stock_notices.txt'
    ],
    
    # 行业板块汇总
    'sector_summary': [
        'sector_summary_'  # 前缀匹配
    ],
    
    # 加速信号
    'acceleration_signals': [
        'acceleration_signals_'  # 前缀匹配
    ],
    
    # 资金流排名
    'fund_flow_rank': [
        'fund_flow_rank_'  # 前缀匹配
    ],
    
    # 主力资金流
    'main_fund_flow': [
        'main_fund_flow_'  # 前缀匹配
    ],
    
    # 股票通知
    'stock_notifications': [
        'stock_notifications.csv'
    ],
    
    # 股票信号
    'stock_signals': [
        'stock_signals.csv'
    ]
}
3. 文件匹配逻辑
pythondef classify_file(self, filename):
    """根据文件名分类文件类型"""
    # 创建所有模式的列表，按长度降序排序（长模式优先匹配）
    all_patterns = []
    for file_type, patterns in self.file_types.items():
        for pattern in patterns:
            all_patterns.append((pattern, file_type))
    all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

    # 逐一匹配模式
    for pattern, file_type in all_patterns:
        if pattern in filename:  # 包含匹配
            return file_type
    return 'other'  # 未匹配到的归类为其他
4. 文件名时间戳提取规则
支持多种时间戳格式：
python# 格式1: HH-MM_ (旧格式)
# 例如: 09-30_zt_pool.csv
time_match = re.search(r'(\d{2})-(\d{2})_', file)

# 格式2: _YYYYMMDD_HHMMSS (新格式)
# 例如: fund_flow_20250725_093000.csv
time_match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', file)

# 格式3: _HHMMSS.csv (新格式)
# 例如: 涨停股池_akshare_东方财富_093651.csv
time_match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', file)
5. 排除文件规则
python# 在 EXCLUDED_FILES 中配置排除的文件
EXCLUDED_FILES = [
    # 'stock_signals.csv'  # 当前已注释，表示包含在分析中
]
这个系统通过文件名模式匹配来自动识别和分类不同类型的金融数据文件，支持多种数据源格式，并且可以提取文件的时间戳信息进行时序分析。