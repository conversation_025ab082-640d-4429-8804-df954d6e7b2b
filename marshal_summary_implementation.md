# 战情总结功能实现文档

## 功能概述

在 `dynamic_gap_detector.py` 中成功新增了 `generate_marshal_summary_report` 函数，该函数作为"战情总结"模块，模拟顶级游资视角对盘面进行高度凝练的解读。

## 核心功能

### 1. 绝对主线识别（主战场确认）
- **识别逻辑**：通过联动校验概念板块龙头分第一与个股资金断层龙头，确认是否形成"将帅合一"格局
- **成功条件**：概念龙头分Top1的板块包含在个股资金龙头的所属概念列表中
- **输出示例**：
  ```
  --- ⚔️ 盘面核心：绝对主线已现 ⚔️ ---
  【绝对主线识别】：人工智能线 (人工智能/计算机设备)
  【核心逻辑】：板块龙头分第一(人工智能)，与全市场资金总龙头【科大讯飞】形成"将帅合一"的最强格局。
  ```

### 2. 奇袭支线识别（高低切换识别）
- **识别逻辑**：分析早盘涨停股（09:40前封板），寻找非主线行业中的板块效应
- **成功条件**：
  - 非主线行业（在行业排名前3之外）
  - 早盘涨停家数 >= 2只
  - 包含20CM涨停龙头（股票代码以'300'或'688'开头）
- **输出示例**：
  ```
  --- 🎯 盘面机会：奇袭支线突现 🎯 ---
  【奇袭支线识别】：科技线
  【核心逻辑】：开盘后迅速形成【奥普特、韦尔股份】等多股联动的板块效应，且先锋【奥普特】为20CM稀缺形态，是高低切换资金攻击的明确信号。
  ```

### 3. 最强补涨识别（主战场内部排序）
- **识别逻辑**：在绝对主线所属行业内，寻找资金强度仅次于龙头的第二强股票
- **成功条件**：
  - 隶属于绝对主线行业
  - 资金流入排名第二
  - 已涨停且形态强硬（早盘封板优先）
- **输出示例**：
  ```
  --- 🚀 核心机会：最强补涨已定 🚀 ---
  【最强补涨识别】：【科大讯飞】
  【核心逻辑】：隶属绝对主线(计算机设备)，板块内资金强度仅次于总龙头【恒润股份】，且涨停形态强硬(早盘封板(09:35:00))，是主线最强的活口和补涨核心。
  ```

## 技术实现

### 函数签名
```python
def generate_marshal_summary_report(market_snapshot, concepts_df, industries_df, stock_flow_data, limit_up_stocks, current_sim_time):
```

### 参数说明
- `market_snapshot` (dict): 包含当前时间点所有关键分析结果的字典
- `concepts_df` (DataFrame): 排序后的概念板块数据
- `industries_df` (DataFrame): 排序后的行业板块数据  
- `stock_flow_data` (DataFrame): 排序后的个股资金流数据
- `limit_up_stocks` (list of dicts): 当前的涨停股池数据
- `current_sim_time` (datetime.time): 当前模拟时间点

### 集成位置
函数已集成到主分析循环 `_run_analysis_core` 的末尾，在 `generate_tiered_watchlist_and_report` 调用之后执行。

### 调用代码
```python
# 在 _run_analysis_core 函数中
if 'all_sectors_df_sorted' in locals() and stock_flow_data is not None:
    # 分离概念和行业数据
    concepts_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '概念']
    industries_df = all_sectors_df_sorted[all_sectors_df_sorted['type'] == '行业']
    
    generate_marshal_summary_report(market_snapshot, concepts_df, industries_df, stock_flow_data, current_limit_up_stocks, current_sim_time)
```

## 辅助函数

### `_identify_absolute_mainline`
负责绝对主线识别逻辑，通过 `get_stock_sectors()` 函数获取股票的板块归属信息。

### `_identify_surprise_sideline`
负责奇袭支线识别逻辑，包含行业名称泛化处理功能。

### `_identify_strongest_follow_up`
负责最强补涨识别逻辑，支持通过行业字段或板块映射两种方式查找同行业股票。

### `_get_generic_industry_name`
将具体行业名称转换为泛行业名称，支持医药、科技、新能源、制造等主要行业分类。

## 错误处理

每个识别函数都包含完整的异常处理机制：
- 数据缺失检查
- 空值处理
- 异常捕获和友好错误提示

## 测试验证

已通过 `test_marshal_summary.py` 进行功能测试，验证了：
- ✅ 绝对主线识别成功场景
- ✅ 奇袭支线识别失败场景（符合预期）
- ✅ 最强补涨识别失败场景（符合预期）
- ✅ 异常处理机制
- ✅ 输出格式正确性

## 使用说明

1. 函数会在每个盘中时间点自动调用
2. 根据当前市场数据自动进行三步识别
3. 直接通过 print 输出战情总结报告
4. 无需手动调用，已集成到主分析流程中

## 特色功能

- **智能联动校验**：通过多模块数据交叉验证，提高识别准确性
- **游资思维模拟**：基于顶级游资的实战经验设计识别逻辑
- **动态阈值判断**：根据市场实时状态调整识别标准
- **友好输出格式**：使用表情符号和结构化文本，便于快速理解

该功能为现有的股票分析系统增加了高层次的战略分析能力，能够帮助用户快速把握市场主线和机会点。
