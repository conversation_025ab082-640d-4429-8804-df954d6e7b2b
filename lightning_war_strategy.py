"""
闪电战策略 - 基于dynamic_gap_detector.py的四因子个股打击分模型
实现"圈定主战区"+"筛选打击群"的量化买入信号生成系统

作者: 基于dynamic_gap_detector.py改造
版本: V1.0
日期: 2025-08-01
"""

import pandas as pd
import numpy as np
import os
import re
import sys
import math
import pickle
from datetime import datetime
from tabulate import tabulate
import warnings

# 导入原有模块
try:
    import fund_flow_query_basic as fq
except ImportError:
    print("警告: 无法导入 fund_flow_query_basic，历史最大值功能将不可用")
    fq = None

# 导入数据库查询模块
import sqlite3

# 导入检测器类和相关函数 - 完全参考dynamic_gap_detector.py
try:
    from dynamic_gap_detector import (
        StockFlowIgnitionDetector,
        HistoricalBreakthroughDetector,
        check_historical_max_inflow,
        apply_stock_filter,
        parse_stock_flow_data,
        get_stock_flow_file_format,
        parse_limit_up_pool_data
    )
except ImportError:
    print("警告: 无法从dynamic_gap_detector导入检测器类，将使用简化版本")

    # 简化版检测器类
    class StockFlowIgnitionDetector:
        def __init__(self):
            pass
        def detect_ignition_signals(self, data, current_time):
            return []

    class HistoricalBreakthroughDetector:
        def __init__(self):
            pass
        def detect_signals(self, data, current_time, market_snapshot, order_flow_tracker):
            return []

    def check_historical_max_inflow(stock_data, query_date):
        """简化版历史最大值检查"""
        stock_data['大于历史资金流入'] = ''
        return stock_data

    def apply_stock_filter(stock_data, file_format):
        """简化版股票过滤"""
        return stock_data

    def parse_stock_flow_data(file_path, file_type):
        """简化版数据解析"""
        return pd.read_csv(file_path, encoding='utf-8')

    def get_stock_flow_file_format(file_path):
        """简化版格式检测"""
        return 'unknown'

    def parse_limit_up_pool_data(file_path):
        """简化版涨停股池解析"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')
            name_columns = ['名称', 'name', '股票名称', '证券名称']
            name_col = next((col for col in name_columns if col in df.columns), None)
            if name_col:
                return [{'名称': row[name_col]} for _, row in df.iterrows()]
            return []
        except:
            return []

def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳，支持多种格式 - 完全参考dynamic_gap_detector.py"""
    # 格式1: HH-MM_ (旧格式) 例如: 09-30_zt_pool.csv, 09-30_ths_big_deal.csv
    match = re.search(r'^(\d{2})-(\d{2})_', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    # 格式2: _YYYYMMDD_HHMMSS (新格式) 例如: fund_flow_rank_20250725_093047.csv
    match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式3: _HHMMSS.csv (新格式) 例如: 涨停股池_akshare_东方财富_093651.csv
    match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式4: _HHMM.csv (简化格式) 例如: 09-34_fund_flow_tpdog.csv
    match = re.search(r'^(\d{2})-(\d{2})_', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    return None


def find_latest_file(file_list, current_time):
    """
    查找最新的文件，确保遵循回测时间限制，不读取未来数据 - 完全参考dynamic_gap_detector.py
    """
    relevant_file = None
    best_timestamp = None

    # 处理所有文件，不要早退出
    for f in sorted(file_list):
        try:
            timestamp = extract_timestamp_from_filename(f)
            if not timestamp: continue
            f_ts = datetime.strptime(timestamp, '%H%M%S').time()

            # 核心回测时间验证：确保不读取未来数据
            if f_ts <= current_time:
                # 如果时间戳更晚，或者时间戳相同但文件名更具体（包含日期），则优先选择
                if (best_timestamp is None or
                    f_ts > best_timestamp or
                    (f_ts == best_timestamp and BACKTEST_DATE.replace('-', '') in f and
                     (relevant_file is None or BACKTEST_DATE.replace('-', '') not in relevant_file))):
                    relevant_file = f
                    best_timestamp = f_ts
        except (ValueError, IndexError):
            continue
    return relevant_file


def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式 - 完全参考dynamic_gap_detector.py"""
    # 大单买盘文件匹配
    big_deal_patterns = [
        'ths_big_deal.csv',       # 同花顺大单买盘
        'movers_大笔买入.csv',     # 大笔买入异动
        'big_deal_',              # 大单买盘通用
        'movers_有大买盘'          # 有大买盘异动
    ]

    # 板块异动文件匹配
    board_changes_patterns = [
        'board_changes.csv',      # 板块异动
        'sector_changes.csv',     # 板块变化
        'concept_changes.csv'     # 概念异动
    ]

    # 涨停股池文件匹配
    limit_up_patterns = [
        'limit_up_pool_',         # 涨停股池
        'zt_pool_',              # 涨停池（新增：支持zt_pool_YYYYMMDD_HHMMSS.csv格式）
        'zt_pool.csv',           # 涨停池
        '涨停股池_',              # 涨停股池中文
        'limit_up_'              # 涨停相关
    ]

    # 创月新高指标文件匹配
    new_high_patterns = [
        'indicator_创月新高.csv',  # 创月新高指标
        'new_high_',             # 新高指标
        'monthly_high_',         # 月新高
        'indicator_'             # 指标文件通用
    ]

    # 个股资金流文件匹配（扩展版）
    stock_flow_patterns = [
        'fund_flow_rank_',        # 个股资金流排名
        'fund_flow_tpdog.csv',    # tpdog个股资金流
        'ths_fund_flow.csv',      # 同花顺个股资金流
        'fund_flow_akshare.csv',  # akshare个股资金流
        'individual_fund_flow_',  # 个股资金流排名
        '股票资金流_zssz_',        # 深圳个股资金流
        '股票资金流_zssh_',        # 上海个股资金流
        'stock_fund_flow_'        # 个股资金流通用
    ]

    # 概念资金流文件匹配（扩展版）
    concept_patterns = [
        'concept_fund_flow_tpdog.csv',
        'concept_fund_flow_akshare.csv',
        'concept_fund_flow_',
        '实时概念资金流_',         # 实时概念资金流
        'realtime_concept_flow_'  # 实时概念流
    ]

    # 行业资金流文件匹配（扩展版）
    sector_patterns = [
        'sector_fund_flow_tpdog.csv',
        'sector_fund_flow_akshare.csv',
        'sector_fund_flow_rank_',
        '实时板块资金流_',         # 实时板块资金流
        'realtime_sector_flow_'   # 实时板块流
    ]

    # 按长度降序排序，长模式优先匹配
    all_patterns = []
    for pattern in big_deal_patterns:
        all_patterns.append((pattern, 'big_deal'))
    for pattern in board_changes_patterns:
        all_patterns.append((pattern, 'board_changes'))
    for pattern in limit_up_patterns:
        all_patterns.append((pattern, 'limit_up'))
    for pattern in new_high_patterns:
        all_patterns.append((pattern, 'new_high'))
    for pattern in stock_flow_patterns:
        all_patterns.append((pattern, 'stock_flow'))
    for pattern in concept_patterns:
        all_patterns.append((pattern, 'concept'))
    for pattern in sector_patterns:
        all_patterns.append((pattern, 'sector'))

    all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

    # 逐一匹配模式
    for pattern, file_type in all_patterns:
        if pattern in filename:
            return file_type

    return 'other'

# 导入原有的检测器和评分器
from dynamic_gap_detector import (
    get_stock_sectors, 
    calculate_sector_leadership_score_v9_4,
    StockFlowIgnitionDetector,
    HistoricalBreakthroughDetector,
    MarketPulseDataPool,
    filter_meaningful_concepts_and_sectors,
    format_amount,
    apply_stock_filter,
    classify_file_type
)

warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# ——— 1. 配置区域 ———
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
BACKTEST_DATE = '2025-07-30'

# 测试模式配置
# ——— 测试模式配置 ———
TEST_MODE = False  # 设置为True时只处理前5个时间点进行测试
MAX_TIMESTAMPS = 10

# ——— 买入信号阈值配置 ———
MIN_BUY_SIGNAL_SCORE = 85  # 大于多少分买入信号（默认70分）


# ——— 2. 闪电战策略配置 ———
LIGHTNING_WAR_CONFIG = {
    # 主战区筛选配置
    'main_battlefield': {
        'min_leadership_score': 0.7,  # 最低龙头评分要求
        'max_battlefields': 2,        # 最多选择2个主战区
        'concept_vs_industry_weight': 0.6  # 概念权重 vs 行业权重
    },
    
    # 个股打击分配置 (满分100分)
    'strike_score': {
        'capital_weight': 40,      # 资金强度分权重 (40%)
        'position_weight': 30,     # 身位优势分权重 (30%)
        'breakthrough_weight': 20, # 历史突破分权重 (20%)
        'ignition_weight': 10,     # 点火信号分权重 (10%)
        'min_total_score': 60      # 最低总分要求
    },
    
    # 打击群规模配置
    'strike_group': {
        'target_size': 30,         # 目标组合规模
        'tier1_max': 10,          # 第一梯队最大数量
        'tier2_max': 15,          # 第二梯队最大数量
        'tier3_max': 5            # 第三梯队最大数量
    },
    
    # 买入信号配置
    'buy_signal': {
        'min_strike_score': MIN_BUY_SIGNAL_SCORE,  # 最低打击分要求（使用文件头配置）
        'min_capital_rank': 50,    # 最低资金排名要求
        'require_limit_up': False, # 是否要求涨停
        'max_signals_per_minute': 5 # 每分钟最大信号数量
    }
}

class DailySignalCollector:
    """全天买入信号收集器"""

    def __init__(self):
        self.all_buy_signals = []  # 存储全天所有买入信号
        self.bought_stocks = set()  # 记录已经买过的股票，避免重复买入

    def add_signals(self, timestamp, buy_signals, stock_flow_data=None):
        """添加买入信号到收集器"""
        for signal in buy_signals:
            stock_name = signal['stock_name']

            # 检查是否已经买过这只股票
            if stock_name in self.bought_stocks:
                print(f"⚠️ {stock_name} 已经买过，跳过重复买入")
                continue

            # 记录已买入的股票
            self.bought_stocks.add(stock_name)

            signal_with_time = signal.copy()
            signal_with_time['买入时间'] = timestamp

            # 尝试获取当时的价格信息
            if stock_flow_data is not None:
                try:
                    stock_row = stock_flow_data[stock_flow_data['名称'] == stock_name]
                    if not stock_row.empty:
                        # 检查是否有价格相关列
                        price_cols = ['现价', '最新价', '价格', '当前价']
                        for col in price_cols:
                            if col in stock_row.columns:
                                signal_with_time['买入价格'] = stock_row.iloc[0][col]
                                break
                        else:
                            signal_with_time['买入价格'] = '未知'
                    else:
                        signal_with_time['买入价格'] = '未知'
                except Exception as e:
                    signal_with_time['买入价格'] = '未知'
            else:
                signal_with_time['买入价格'] = '未知'

            self.all_buy_signals.append(signal_with_time)

    def get_daily_summary(self):
        """获取全天买入信号汇总"""
        if not self.all_buy_signals:
            return "📊 全天买入信号汇总: 无买入信号"

        # 按时间排序
        sorted_signals = sorted(self.all_buy_signals, key=lambda x: x['买入时间'])

        summary_lines = []
        summary_lines.append(f"\n📊 全天买入信号汇总 ({len(sorted_signals)}个)")
        summary_lines.append("=" * 80)

        # 创建表格数据
        table_data = []
        for i, signal in enumerate(sorted_signals, 1):
            table_data.append([
                i,
                signal['买入时间'],
                signal['stock_name'],  # 修复：使用正确的键名
                f"{signal['strike_score']:.0f}",  # 修复：使用正确的键名
                signal['capital_rank'],  # 修复：使用正确的键名
                signal['tier'],
                "✅" if signal.get('is_limit_up', False) else "",
                signal.get('买入价格', '未知'),
                signal['net_inflow'],  # 修复：使用正确的键名
                signal['buy_reason']  # 修复：使用正确的键名
            ])

        # 生成表格
        headers = ['序号', '买入时间', '股票名称', '打击分', '资金排名', '所属梯队', '涨停', '买入价格', '净流入', '买入理由']
        table_str = tabulate(table_data, headers=headers, tablefmt='grid', showindex=False)
        summary_lines.append(table_str)

        return "\n".join(summary_lines)

class LightningWarStrategy:
    """闪电战策略核心类"""

    def __init__(self):
        # 初始化检测器
        self.ignition_detector = StockFlowIgnitionDetector()
        self.breakthrough_detector = HistoricalBreakthroughDetector()

        # 历史数据缓存
        self.previous_data = None
        self.battlefield_history = []  # 主战区历史
        self.signal_history = []       # 信号历史
        
    def identify_main_battlefields(self, all_sectors_df, limit_up_stocks, current_time=None):
        """
        第一步：圈定主战区 - 寻找最强合力点
        
        参数:
        - all_sectors_df: 板块资金流数据
        - limit_up_stocks: 涨停股数据
        - current_time: 当前时间
        
        返回:
        - dict: {'concept_top1': str, 'industry_top1': str, 'selected_battlefields': list}
        """
        try:
            # 使用现有的龙头评分函数计算板块强度
            scored_sectors = calculate_sector_leadership_score_v9_4(
                all_sectors_df, limit_up_stocks, current_time
            )
            
            if scored_sectors.empty:
                return {'concept_top1': None, 'industry_top1': None, 'selected_battlefields': []}
            
            # 分离概念和行业板块
            concept_sectors = []
            industry_sectors = []
            
            for _, sector in scored_sectors.iterrows():
                sector_name = sector['名称']
                # 简单的概念/行业分类逻辑（可根据实际情况优化）
                if any(keyword in sector_name for keyword in ['概念', 'AI', '5G', '新能源', '芯片', '半导体']):
                    concept_sectors.append(sector)
                else:
                    industry_sectors.append(sector)
            
            # 找出概念Top1和行业Top1
            concept_top1 = None
            industry_top1 = None
            
            if concept_sectors:
                concept_df = pd.DataFrame(concept_sectors)
                concept_top1 = concept_df.loc[concept_df['leadership_score'].idxmax(), '名称']
            
            if industry_sectors:
                industry_df = pd.DataFrame(industry_sectors)
                industry_top1 = industry_df.loc[industry_df['leadership_score'].idxmax(), '名称']
            
            # 在概念Top1和行业Top1之间优中选优
            selected_battlefields = []
            config = LIGHTNING_WAR_CONFIG['main_battlefield']
            
            # 获取两个候选战区的详细信息
            candidates = []
            if concept_top1:
                concept_info = scored_sectors[scored_sectors['名称'] == concept_top1].iloc[0]
                if concept_info['leadership_score'] >= config['min_leadership_score']:
                    candidates.append({
                        'name': concept_top1,
                        'type': 'concept',
                        'score': concept_info['leadership_score'],
                        'data': concept_info
                    })
            
            if industry_top1:
                industry_info = scored_sectors[scored_sectors['名称'] == industry_top1].iloc[0]
                if industry_info['leadership_score'] >= config['min_leadership_score']:
                    candidates.append({
                        'name': industry_top1,
                        'type': 'industry', 
                        'score': industry_info['leadership_score'],
                        'data': industry_info
                    })
            
            # 按评分排序，选择最强的1-2个战区
            candidates.sort(key=lambda x: x['score'], reverse=True)
            selected_battlefields = candidates[:config['max_battlefields']]
            
            print(f"\n🎯 【主战区识别结果】")
            print(f"概念Top1: {concept_top1} (评分: {concept_info['leadership_score']:.3f})" if concept_top1 else "概念Top1: 无")
            print(f"行业Top1: {industry_top1} (评分: {industry_info['leadership_score']:.3f})" if industry_top1 else "行业Top1: 无")
            print(f"选定主战区: {[bf['name'] for bf in selected_battlefields]}")
            
            return {
                'concept_top1': concept_top1,
                'industry_top1': industry_top1,
                'selected_battlefields': selected_battlefields
            }

        except Exception as e:
            print(f"❌ 主战区识别失败: {e}")
            return {'concept_top1': None, 'industry_top1': None, 'selected_battlefields': []}

    def calculate_individual_strike_score(self, stock_data, stock_rank, battlefields,
                                        breakthrough_signals=None, ignition_signals=None):
        """
        第二步：计算个股打击分 - 四因子加权评分模型 (满分100分)

        参数:
        - stock_data: 个股数据 (Series)
        - stock_rank: 个股资金排名
        - battlefields: 主战区列表
        - breakthrough_signals: 历史突破信号列表
        - ignition_signals: 点火信号列表

        返回:
        - dict: 包含各因子得分和总分的详细信息
        """
        try:
            # 确保stock_data包含必要的列
            if '名称' not in stock_data:
                print(f"⚠️ 股票数据缺少'名称'列: {stock_data.index}")
                return None

            stock_name = stock_data['名称']
            config = LIGHTNING_WAR_CONFIG['strike_score']

            # 因子一：资金强度分 (权重: 40%)
            capital_score = self._calculate_capital_strength_score(stock_rank)

            # 因子二：身位优势分 (权重: 30%)
            position_score = self._calculate_position_advantage_score(stock_data)

            # 因子三：历史突破加分 (权重: 20%)
            breakthrough_score = self._calculate_breakthrough_score(stock_name, breakthrough_signals)

            # 因子四：点火信号加分 (权重: 10%)
            ignition_score = self._calculate_ignition_score(stock_name, ignition_signals)

            # 板块加成：检查是否属于主战区
            battlefield_bonus = self._calculate_battlefield_bonus(stock_name, battlefields)

            # 计算加权总分
            # 注意：各个子函数返回的已经是加权后的分数，直接相加即可
            weighted_total = (
                capital_score +
                position_score +
                breakthrough_score +
                ignition_score
            ) + battlefield_bonus

            # 确保总分不超过100分
            final_score = min(100, weighted_total)

            return {
                'stock_name': stock_name,
                'capital_score': capital_score,
                'position_score': position_score,
                'breakthrough_score': breakthrough_score,
                'ignition_score': ignition_score,
                'battlefield_bonus': battlefield_bonus,
                'final_score': final_score,
                'rank': stock_rank,
                'net_inflow': stock_data.get('今日主力净流入-净额', 0),
                'change_pct': stock_data.get('今日涨跌幅', 0)
            }

        except Exception as e:
            print(f"❌ 计算个股打击分失败 ({stock_name}): {e}")
            return None

    def _calculate_capital_strength_score(self, rank):
        """计算资金强度分 - 基于资金流排名 (满分40分)"""
        if rank <= 0:
            return 0

        if rank <= 10:
            return 40  # 前10名满分
        elif rank <= 20:
            return 35  # 前20名高分
        elif rank <= 30:
            return 30  # 前30名中等
        elif rank <= 50:
            return 20  # 前50名基础分
        else:
            return max(0, 20 - (rank - 50) * 0.5)  # 50名后递减

    def _calculate_position_advantage_score(self, stock_data):
        """计算身位优势分 - 基于涨停和涨幅 (满分30分)"""
        change_pct = stock_data.get('今日涨跌幅', 0)

        if not isinstance(change_pct, (int, float)):
            return 0

        # 涨停：满分30分
        if change_pct >= 9.8:
            return 30
        # 接近涨停 (>9.5%): 25分
        elif change_pct > 9.5:
            return 25
        # 大涨 (>7%): 20分
        elif change_pct > 7:
            return 20
        # 中涨 (>5%): 15分
        elif change_pct > 5:
            return 15
        # 小涨 (>3%): 10分
        elif change_pct > 3:
            return 10
        # 微涨 (>0%): 5分
        elif change_pct > 0:
            return 5
        else:
            return 0

    def _calculate_breakthrough_score(self, stock_name, breakthrough_signals):
        """计算历史突破加分 - 基于HistoricalBreakthroughDetector输出 (满分20分)"""
        if not breakthrough_signals:
            return 0

        # 查找该股票的突破信号
        for signal in breakthrough_signals:
            if signal.get('股票名称') == stock_name:
                comprehensive_score = signal.get('综合评分', 0)
                # 将综合评分转换为20分制
                return min(20, comprehensive_score * 2)

        return 0

    def _calculate_ignition_score(self, stock_name, ignition_signals):
        """计算点火信号加分 - 基于StockFlowIgnitionDetector输出 (满分10分)"""
        if not ignition_signals:
            return 0

        # 查找该股票的点火信号
        for signal in ignition_signals:
            if signal.get('股票名称') == stock_name:
                signal_score = signal.get('score', 0)
                # 将信号评分转换为10分制
                return min(10, signal_score)

        return 0

    def _calculate_battlefield_bonus(self, stock_name, battlefields):
        """计算主战区加成 - 属于主战区的股票获得额外加分"""
        if not battlefields:
            return 0

        # 获取股票的板块信息
        sectors_info = get_stock_sectors(stock_name)
        stock_sectors = sectors_info.get('concepts', []) + sectors_info.get('industries', [])

        # 检查是否属于任何主战区
        for battlefield in battlefields:
            battlefield_name = battlefield['name']
            if battlefield_name in stock_sectors:
                # 主战区加成：根据战区评分给予5-15分加成
                battlefield_score = battlefield['score']
                if battlefield_score >= 0.9:
                    return 15  # 超强战区
                elif battlefield_score >= 0.8:
                    return 12  # 强势战区
                elif battlefield_score >= 0.7:
                    return 8   # 中等战区
                else:
                    return 5   # 基础战区

        return 0

    def select_strike_group(self, stock_flow_data, battlefields, breakthrough_signals=None, ignition_signals=None):
        """
        第三步：筛选打击群 - 构建约30只股票的组合

        参数:
        - stock_flow_data: 个股资金流数据
        - battlefields: 主战区列表
        - breakthrough_signals: 历史突破信号
        - ignition_signals: 点火信号

        返回:
        - dict: 包含三个梯队的打击群组合
        """
        try:
            config = LIGHTNING_WAR_CONFIG['strike_group']
            min_score = LIGHTNING_WAR_CONFIG['strike_score']['min_total_score']

            # 计算所有股票的打击分
            strike_candidates = []

            for idx, stock_data in stock_flow_data.iterrows():
                stock_rank = idx + 1  # 排名从1开始

                # 计算个股打击分
                strike_info = self.calculate_individual_strike_score(
                    stock_data, stock_rank, battlefields, breakthrough_signals, ignition_signals
                )

                if strike_info and strike_info['final_score'] >= min_score:
                    strike_candidates.append(strike_info)

            # 按打击分排序
            strike_candidates.sort(key=lambda x: x['final_score'], reverse=True)

            # 分配到三个梯队
            tier1_stocks = []  # 第一梯队：主战场龙头
            tier2_stocks = []  # 第二梯队：板块中军
            tier3_stocks = []  # 第三梯队：独立强势股

            for candidate in strike_candidates:
                # 第一梯队：高打击分 + 主战区 + 强势身位
                if (len(tier1_stocks) < config['tier1_max'] and
                    candidate['final_score'] >= 80 and
                    candidate['battlefield_bonus'] > 0 and
                    candidate['position_score'] >= 20):
                    tier1_stocks.append(candidate)

                # 第二梯队：中等打击分 + 主战区或强势资金
                elif (len(tier2_stocks) < config['tier2_max'] and
                      candidate['final_score'] >= 70 and
                      (candidate['battlefield_bonus'] > 0 or candidate['rank'] <= 30)):
                    tier2_stocks.append(candidate)

                # 第三梯队：独立强势股（非主战区但资金排名极高）
                elif (len(tier3_stocks) < config['tier3_max'] and
                      candidate['final_score'] >= 65 and
                      candidate['battlefield_bonus'] == 0 and
                      candidate['rank'] <= 10):
                    tier3_stocks.append(candidate)

                # 检查是否达到目标规模
                total_selected = len(tier1_stocks) + len(tier2_stocks) + len(tier3_stocks)
                if total_selected >= config['target_size']:
                    break

            return {
                'tier1': tier1_stocks,
                'tier2': tier2_stocks,
                'tier3': tier3_stocks,
                'total_count': len(tier1_stocks) + len(tier2_stocks) + len(tier3_stocks)
            }

        except Exception as e:
            print(f"❌ 打击群筛选失败: {e}")
            return {'tier1': [], 'tier2': [], 'tier3': [], 'total_count': 0}

    def generate_buy_signals(self, strike_group, current_time, limit_up_stocks=None):
        """
        第四步：生成买入信号 - 基于打击分和实时条件

        参数:
        - strike_group: 打击群组合
        - current_time: 当前时间
        - limit_up_stocks: 涨停股数据

        返回:
        - list: 买入信号列表
        """
        try:
            config = LIGHTNING_WAR_CONFIG['buy_signal']
            buy_signals = []

            # 合并所有梯队
            all_candidates = (strike_group['tier1'] +
                            strike_group['tier2'] +
                            strike_group['tier3'])

            # 按打击分排序
            all_candidates.sort(key=lambda x: x['final_score'], reverse=True)

            for candidate in all_candidates:
                # 检查买入条件
                if not self._check_buy_conditions(candidate, config, limit_up_stocks):
                    continue

                # 生成买入信号
                signal = {
                    'timestamp': current_time,
                    'stock_name': candidate['stock_name'],
                    'strike_score': candidate['final_score'],
                    'capital_rank': candidate['rank'],
                    'net_inflow': candidate['net_inflow'],
                    'change_pct': candidate['change_pct'],
                    'signal_strength': self._calculate_signal_strength(candidate),
                    'tier': self._determine_tier(candidate, strike_group),
                    'buy_reason': self._generate_buy_reason(candidate),
                    'risk_level': self._assess_risk_level(candidate, limit_up_stocks)
                }

                buy_signals.append(signal)

                # 限制每分钟信号数量
                if len(buy_signals) >= config['max_signals_per_minute']:
                    break

            return buy_signals

        except Exception as e:
            print(f"❌ 买入信号生成失败: {e}")
            return []

    def _check_buy_conditions(self, candidate, config, limit_up_stocks):
        """检查买入条件 - 确保不使用未来数据"""
        # 基础条件检查
        if candidate['final_score'] < config['min_strike_score']:
            return False

        if candidate['rank'] > config['min_capital_rank']:
            return False

        # 【关键】检查股票是否已经涨停 - 涨停了就无法买入
        if self._is_already_limit_up(candidate['stock_name'], limit_up_stocks):
            return False

        # 涨停要求检查（如果配置要求涨停）
        if config['require_limit_up']:
            if candidate['change_pct'] < 9.8:
                return False

        # 一字板过滤（避免买入无法成交的一字板）
        if self._is_one_word_board(candidate['stock_name'], limit_up_stocks):
            return False

        return True

    def _is_already_limit_up(self, stock_name, limit_up_stocks):
        """检查股票是否已经涨停 - 涨停了就无法买入"""
        if not limit_up_stocks:
            return False

        # 检查股票是否在涨停股池中
        limit_up_names = [stock.get('名称', '') for stock in limit_up_stocks]
        is_limit_up = stock_name in limit_up_names

        # 调试信息
        if is_limit_up:
            print(f"🚫 过滤涨停股: {stock_name}")

        return is_limit_up

    def _is_one_word_board(self, stock_name, limit_up_stocks):
        """判断是否为一字板"""
        if not limit_up_stocks:
            return False

        for stock in limit_up_stocks:
            if stock.get('名称') == stock_name:
                # 检查炸板次数和首次封板时间
                break_count = stock.get('炸板次数', 1)
                first_seal_time = stock.get('首次封板时间', '')

                if break_count == 0 and first_seal_time and first_seal_time <= "09:30:00":
                    return True

        return False

    def _calculate_signal_strength(self, candidate):
        """计算信号强度"""
        score = candidate['final_score']
        if score >= 90:
            return "极强"
        elif score >= 80:
            return "强"
        elif score >= 70:
            return "中等"
        else:
            return "弱"

    def _determine_tier(self, candidate, strike_group):
        """确定股票所属梯队"""
        if candidate in strike_group['tier1']:
            return "第一梯队"
        elif candidate in strike_group['tier2']:
            return "第二梯队"
        elif candidate in strike_group['tier3']:
            return "第三梯队"
        else:
            return "未分类"

    def _generate_buy_reason(self, candidate):
        """生成买入理由"""
        reasons = []

        if candidate['capital_score'] >= 35:
            reasons.append("资金强势")

        if candidate['position_score'] >= 25:
            reasons.append("身位优势")

        if candidate['breakthrough_score'] > 0:
            reasons.append("历史突破")

        if candidate['ignition_score'] > 0:
            reasons.append("点火信号")

        if candidate['battlefield_bonus'] > 0:
            reasons.append("主战区")

        return " + ".join(reasons) if reasons else "综合评分达标"

    def _assess_risk_level(self, candidate, limit_up_stocks):
        """评估风险等级"""
        risk_score = 0

        # 涨幅风险
        if candidate['change_pct'] >= 9.8:
            risk_score += 3
        elif candidate['change_pct'] >= 7:
            risk_score += 2
        elif candidate['change_pct'] >= 5:
            risk_score += 1

        # 排名风险
        if candidate['rank'] > 30:
            risk_score += 2
        elif candidate['rank'] > 20:
            risk_score += 1

        # 连板风险（如果有涨停股数据）
        if limit_up_stocks:
            for stock in limit_up_stocks:
                if stock.get('名称') == candidate['stock_name']:
                    consecutive_days = stock.get('连板数', 1)
                    if consecutive_days >= 5:
                        risk_score += 3
                    elif consecutive_days >= 3:
                        risk_score += 2
                    elif consecutive_days >= 2:
                        risk_score += 1
                    break

        if risk_score >= 6:
            return "高风险"
        elif risk_score >= 4:
            return "中风险"
        elif risk_score >= 2:
            return "低风险"
        else:
            return "极低风险"

def execute_lightning_war_strategy(stock_flow_data, all_sectors_df, limit_up_stocks=None,
                                 current_time=None, output_file=None):
    """
    执行闪电战策略的主函数

    参数:
    - stock_flow_data: 个股资金流数据
    - all_sectors_df: 板块资金流数据
    - limit_up_stocks: 涨停股数据
    - current_time: 当前时间
    - output_file: 输出文件路径

    返回:
    - dict: 策略执行结果
    """
    try:
        print(f"\n{'='*60}")
        print(f"🚀 闪电战策略执行开始 - {current_time}")
        print(f"{'='*60}")

        # 初始化策略
        strategy = LightningWarStrategy()

        # 第一步：圈定主战区
        print(f"\n📍 第一步：圈定主战区")
        battlefields_result = strategy.identify_main_battlefields(
            all_sectors_df, limit_up_stocks, current_time
        )

        if not battlefields_result['selected_battlefields']:
            print("❌ 未找到合适的主战区，策略终止")
            return {'success': False, 'reason': '无合适主战区'}

        # 第二步：检测突破和点火信号 - 完全参考dynamic_gap_detector.py
        print(f"\n🔍 第二步：检测历史突破和点火信号")

        # 转换时间格式为datetime对象
        if isinstance(current_time, str):
            try:
                if len(current_time) == 19:  # YYYY-MM-DD HH:MM:SS格式
                    current_time_obj = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
                else:
                    current_time_obj = datetime.now()
            except:
                current_time_obj = datetime.now()
        else:
            current_time_obj = current_time or datetime.now()

        # 点火信号检测 - 完全参考dynamic_gap_detector.py的调用方式
        ignition_signals = []
        if '今日主力净流入-净额' in stock_flow_data.columns:
            positive_flow_df = stock_flow_data[stock_flow_data['今日主力净流入-净额'] > 0]
            if not positive_flow_df.empty and len(positive_flow_df) >= 2:
                try:
                    ignition_signals = strategy.ignition_detector.detect_ignition_signals(
                        positive_flow_df, current_time_obj.time()
                    )
                except Exception as e:
                    print(f"⚠️ 点火信号检测失败: {e}")
        else:
            print("⚠️ 未找到'今日主力净流入-净额'列，跳过点火信号检测")

        # 历史突破信号检测 - 完全参考dynamic_gap_detector.py的调用方式
        breakthrough_signals = []
        if not stock_flow_data.empty and len(stock_flow_data) >= 2:
            try:
                # 创建market_snapshot（简化版）
                market_snapshot = {
                    'limit_up_stocks': [],
                    'accelerating_sectors': []
                }

                # 创建order_flow_tracker（简化版）
                order_flow_tracker = {}

                breakthrough_signals = strategy.breakthrough_detector.detect_signals(
                    stock_flow_data, current_time_obj.time(), market_snapshot, order_flow_tracker
                )
            except Exception as e:
                print(f"⚠️ 历史突破信号检测失败: {e}")

        print(f"检测到历史突破信号: {len(breakthrough_signals)}个")
        print(f"检测到点火信号: {len(ignition_signals)}个")

        # 第三步：筛选打击群
        print(f"\n🎯 第三步：筛选打击群")
        strike_group = strategy.select_strike_group(
            stock_flow_data, battlefields_result['selected_battlefields'],
            breakthrough_signals, ignition_signals
        )

        print(f"打击群规模: 第一梯队{len(strike_group['tier1'])}只, "
              f"第二梯队{len(strike_group['tier2'])}只, "
              f"第三梯队{len(strike_group['tier3'])}只, "
              f"总计{strike_group['total_count']}只")

        # 第四步：生成买入信号
        print(f"\n💰 第四步：生成买入信号")
        buy_signals = strategy.generate_buy_signals(
            strike_group, current_time, limit_up_stocks
        )

        print(f"生成买入信号: {len(buy_signals)}个")

        # 第五步：输出结果
        result = {
            'success': True,
            'timestamp': current_time,
            'battlefields': battlefields_result,
            'strike_group': strike_group,
            'buy_signals': buy_signals,
            'breakthrough_signals': breakthrough_signals,
            'ignition_signals': ignition_signals
        }

        # 生成报告
        report = generate_lightning_war_report(result)
        print(report)

        # 输出到文件
        if output_file:
            save_lightning_war_signals(result, output_file)

        return result

    except Exception as e:
        print(f"❌ 闪电战策略执行失败: {e}")
        return {'success': False, 'reason': str(e)}

def generate_lightning_war_report(result):
    """生成闪电战策略报告"""
    try:
        report_lines = []

        # 标题
        timestamp = result.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        report_lines.append(f"\n🚀 闪电战策略报告 - {timestamp}")
        report_lines.append("=" * 60)

        # 主战区信息
        battlefields = result.get('battlefields', {})
        selected_battlefields = battlefields.get('selected_battlefields', [])

        report_lines.append(f"\n📍 主战区识别结果:")
        if selected_battlefields:
            for i, bf in enumerate(selected_battlefields, 1):
                report_lines.append(f"  {i}. {bf['name']} (类型: {bf['type']}, 评分: {bf['score']:.3f})")
        else:
            report_lines.append("  无合适主战区")

        # 打击群统计
        strike_group = result.get('strike_group', {})
        report_lines.append(f"\n🎯 打击群构成:")
        report_lines.append(f"  第一梯队: {len(strike_group.get('tier1', []))}只")
        report_lines.append(f"  第二梯队: {len(strike_group.get('tier2', []))}只")
        report_lines.append(f"  第三梯队: {len(strike_group.get('tier3', []))}只")
        report_lines.append(f"  总计: {strike_group.get('total_count', 0)}只")

        # 买入信号详情
        buy_signals = result.get('buy_signals', [])
        report_lines.append(f"\n💰 买入信号 ({len(buy_signals)}个):")

        if buy_signals:
            # 创建表格
            table_data = []
            for i, signal in enumerate(buy_signals, 1):
                table_data.append([
                    i,
                    signal['stock_name'],
                    f"{signal['strike_score']:.1f}",
                    signal['capital_rank'],
                    signal['tier'],
                    "✅" if signal['change_pct'] >= 9.8 else "",
                    format_amount(signal['net_inflow']),
                    signal['buy_reason']
                ])

            table_str = tabulate(table_data,
                               headers=['优先级', '股票名称', '打击分', '资金排名', '所属梯队', '涨停', '净流入', '买入理由'],
                               tablefmt='psql', showindex=False)
            report_lines.append(table_str)
        else:
            report_lines.append("  暂无买入信号")

        # 信号统计
        breakthrough_signals = result.get('breakthrough_signals', [])
        ignition_signals = result.get('ignition_signals', [])

        report_lines.append(f"\n📊 信号统计:")
        report_lines.append(f"  历史突破信号: {len(breakthrough_signals)}个")
        report_lines.append(f"  点火信号: {len(ignition_signals)}个")
        report_lines.append(f"  买入信号: {len(buy_signals)}个")

        return "\n".join(report_lines)

    except Exception as e:
        return f"❌ 报告生成失败: {e}"

def save_lightning_war_signals(result, output_file):
    """保存闪电战信号到文件（参考core_pool_signals格式）"""
    try:
        timestamp = result.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        buy_signals = result.get('buy_signals', [])

        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== 闪电战策略信号 - {timestamp} ===\n")
            f.write("策略说明：圈定主战区 + 四因子打击分模型\n")
            f.write("评分公式：打击分 = 资金强度分(40%) + 身位优势分(30%) + 历史突破分(20%) + 点火信号分(10%) + 主战区加成\n")
            f.write("-" * 80 + "\n\n")

            if buy_signals:
                # 按梯队分组输出
                tier_groups = {}
                for signal in buy_signals:
                    tier = signal['tier']
                    if tier not in tier_groups:
                        tier_groups[tier] = []
                    tier_groups[tier].append(signal)

                for tier_name in ['第一梯队', '第二梯队', '第三梯队']:
                    if tier_name in tier_groups:
                        f.write(f"{tier_name}：\n")

                        # 创建表格数据
                        table_data = []
                        for i, signal in enumerate(tier_groups[tier_name], 1):
                            table_data.append([
                                i,
                                signal['stock_name'],
                                f"{signal['strike_score']:.1f}",
                                signal['capital_rank'],
                                signal['tier'],
                                "✅" if signal['change_pct'] >= 9.8 else "",
                                format_amount(signal['net_inflow']),
                                signal['buy_reason']
                            ])

                        # 输出表格
                        table_str = tabulate(table_data,
                                           headers=['优先级', '股票名称', '打击分', '资金排名', '所属梯队', '涨停', '净流入', '买入理由'],
                                           tablefmt='psql', showindex=False)
                        f.write(table_str + "\n\n")
            else:
                f.write("暂无买入信号\n\n")

        print(f"✅ 信号已保存到: {output_file}")

    except Exception as e:
        print(f"❌ 信号保存失败: {e}")

def run_lightning_war_backtest(date_str, data_dir=None):
    """
    运行闪电战策略回测

    参数:
    - date_str: 日期字符串 (格式: YYYY-MM-DD)
    - data_dir: 数据目录路径 (可选，默认使用配置的BASE_DATA_DIR)

    返回:
    - dict: 回测结果
    """
    try:
        if not data_dir:
            # 参考dynamic_gap_detector.py的路径构建方式
            data_dir = os.path.join(BASE_DATA_DIR, date_str)

        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            return {'success': False, 'reason': '数据目录不存在'}

        # 获取所有CSV文件
        all_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

        # 分类文件 - 完全参考dynamic_gap_detector.py的方式
        industry_files = []
        concept_files = []
        stock_flow_files = []
        limit_up_files = []

        for file in all_files:
            if file.endswith('.csv'):
                file_type = classify_file_type(file)
                if file_type == 'sector':
                    industry_files.append(file)
                elif file_type == 'concept':
                    concept_files.append(file)
                elif file_type == 'stock_flow':
                    stock_flow_files.append(file)
                elif file_type == 'limit_up':
                    limit_up_files.append(file)

        industry_files = sorted(industry_files)
        concept_files = sorted(concept_files)
        stock_flow_files = sorted(stock_flow_files)
        limit_up_files = sorted(limit_up_files)

        total_sector_files = len(industry_files) + len(concept_files)
        print(f"📁 找到文件: 个股{len(stock_flow_files)}个, 板块{total_sector_files}个(行业{len(industry_files)}+概念{len(concept_files)}), 涨停{len(limit_up_files)}个")

        # 输出文件
        output_file = f"lightning_war_signals_{date_str}.txt"

        # 清空输出文件（每次运行都覆盖写入）
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("")  # 清空文件
            print(f"📄 输出文件已清空: {output_file}")
        except Exception as e:
            print(f"⚠️ 清空输出文件失败: {e}")

        # 初始化全天信号收集器
        daily_collector = DailySignalCollector()

        # 完全参考dynamic_gap_detector.py的时间点处理方式
        # 修复时间戳提取逻辑：支持多种时间戳格式
        timestamps = set()
        all_data_files = (industry_files + concept_files + stock_flow_files + limit_up_files)
        for f in all_data_files:
            timestamp = extract_timestamp_from_filename(f)
            if timestamp:
                timestamps.add(timestamp)
        timestamps = sorted(list(timestamps))

        print(f"找到 {len(timestamps)} 个时间点")
        if not timestamps:
            print("⚠️ 未找到任何时间戳，请检查文件名格式")
            return

        # 测试模式：限制处理的时间点数量
        if TEST_MODE:
            timestamps = timestamps[:MAX_TIMESTAMPS]
            print(f"🧪 测试模式：只处理前 {len(timestamps)} 个时间点")

        results = []
        daily_collector = DailySignalCollector()  # 创建全天信号收集器

        for ts_str in timestamps:
            current_sim_time = datetime.strptime(ts_str, '%H%M%S').time()

            # 只处理交易时间
            if not (datetime.strptime("09:30", "%H:%M").time() <= current_sim_time <= datetime.strptime("11:30", "%H:%M").time() or \
                    current_sim_time >= datetime.strptime("13:00", "%H:%M").time()):
                continue

            print(f"\n⏰ 处理时间点: {current_sim_time}")

            # 使用find_latest_file查找对应的文件
            latest_industry_file = find_latest_file(industry_files, current_sim_time)
            latest_concept_file = find_latest_file(concept_files, current_sim_time)
            latest_stock_flow_file = find_latest_file(stock_flow_files, current_sim_time)
            latest_limit_up_file = find_latest_file(limit_up_files, current_sim_time)

            if not latest_stock_flow_file:
                print(f"⚠️ 未找到对应的个股资金流文件")
                continue

            # 加载个股数据 - 完全参考dynamic_gap_detector.py的方式
            stock_flow_file_path = os.path.join(data_dir, latest_stock_flow_file)
            stock_flow_data = parse_stock_flow_data(stock_flow_file_path, 'stock_flow')
            stock_flow_file_format = get_stock_flow_file_format(stock_flow_file_path)

            if stock_flow_data is None or stock_flow_data.empty:
                print(f"⚠️ 个股资金流数据为空")
                continue

            # 应用股票过滤器
            positive_stocks = apply_stock_filter(stock_flow_data, stock_flow_file_format)
            if positive_stocks.empty:
                print(f"⚠️ 过滤后无有效股票数据")
                continue

            # 取前50名并添加历史最大值检查
            display_stocks = positive_stocks.head(50).copy()
            display_stocks = check_historical_max_inflow(display_stocks, date_str)

            # 使用处理后的数据
            stock_flow_data = display_stocks

            # 合并行业和概念数据 - 完全参考dynamic_gap_detector.py
            all_sectors_list = []
            if latest_industry_file:
                try:
                    df_ind = pd.read_csv(os.path.join(data_dir, latest_industry_file), encoding='utf-8-sig', on_bad_lines='skip')
                    if '名称' in df_ind.columns and '今日主力净流入-净额' in df_ind.columns:
                        df_ind['type'] = '行业'
                        all_sectors_list.append(df_ind[['名称', '今日主力净流入-净额', 'type']])
                except Exception as e:
                    print(f"读取行业文件失败: {e}")

            if latest_concept_file:
                try:
                    df_con = pd.read_csv(os.path.join(data_dir, latest_concept_file), encoding='utf-8-sig', on_bad_lines='skip')
                    if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
                        df_con['type'] = '概念'
                        all_sectors_list.append(df_con[['名称', '今日主力净流入-净额', 'type']])
                except Exception as e:
                    print(f"读取概念文件失败: {e}")

            if not all_sectors_list:
                print(f"⚠️ 未找到对应的板块数据文件")
                continue

            # 合并所有板块数据
            all_sectors_df = pd.concat(all_sectors_list, ignore_index=True)

            # 查找对应的涨停股数据 - 完全参考dynamic_gap_detector.py
            limit_up_stocks = []
            if latest_limit_up_file:
                try:
                    limit_up_file_path = os.path.join(data_dir, latest_limit_up_file)
                    limit_up_stocks = parse_limit_up_pool_data(limit_up_file_path)
                    print(f"📈 涨停股数据: {len(limit_up_stocks)}只")

                    # 调试信息：显示前3只涨停股
                    if limit_up_stocks:
                        print(f"🔍 涨停股样本: {[stock.get('名称', 'N/A') for stock in limit_up_stocks[:3]]}")
                except Exception as e:
                    print(f"⚠️ 读取涨停股文件失败: {e}")
                    limit_up_stocks = []

            # 执行策略
            try:
                result = execute_lightning_war_strategy(
                    stock_flow_data, all_sectors_df, limit_up_stocks,
                    f"{date_str} {current_sim_time}", output_file
                )

                if result['success']:
                    results.append(result)

                    # 将买入信号添加到全天收集器
                    if 'buy_signals' in result and result['buy_signals']:
                        daily_collector.add_signals(
                            f"{current_sim_time}",
                            result['buy_signals'],
                            stock_flow_data
                        )
            except Exception as e:
                print(f"❌ 策略执行失败: {e}")
                continue

        print(f"\n✅ 回测完成，共处理{len(results)}个时间点")

        # 输出全天买入信号汇总
        daily_summary = daily_collector.get_daily_summary()
        print(daily_summary)

        # 将汇总也保存到文件
        try:
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(f"\n\n{daily_summary}\n")
            print(f"✅ 全天汇总已追加到: {output_file}")
        except Exception as e:
            print(f"⚠️ 汇总保存失败: {e}")

        return {
            'success': True,
            'results': results,
            'output_file': output_file,
            'daily_signals': daily_collector.all_buy_signals,
            'daily_summary': daily_summary
        }

    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return {'success': False, 'reason': str(e)}

if __name__ == "__main__":
    """主程序入口 - 参考dynamic_gap_detector.py的运行方式"""
    print(f"🚀 闪电战策略回测")
    print(f"=" * 50)
    print(f"📋 配置参数:")
    print(f"   回测日期: {BACKTEST_DATE}")
    print(f"   数据目录: {os.path.join(BASE_DATA_DIR, BACKTEST_DATE)}")
    print(f"   买入信号阈值: {MIN_BUY_SIGNAL_SCORE}分")
    print(f"=" * 50)

    # 直接调用回测函数，传入配置的日期
    result = run_lightning_war_backtest(BACKTEST_DATE)

    if result['success']:
        print(f"\n🎉 回测成功完成！")
        print(f"📄 输出文件: {result['output_file']}")

        # 显示详细统计
        if 'results' in result and result['results']:
            total_signals = sum(len(r.get('buy_signals', [])) for r in result['results'])
            print(f"📊 回测统计:")
            print(f"   处理时间点: {len(result['results'])}个")
            print(f"   总买入信号: {total_signals}个")

            # 显示全天买入信号的简要信息
            if 'daily_signals' in result and result['daily_signals']:
                unique_stocks = set(signal['stock_name'] for signal in result['daily_signals'])  # 修复键名
                print(f"   涉及股票: {len(unique_stocks)}只")
                print(f"   平均每时间点: {total_signals/len(result['results']):.1f}个信号")

                # 显示最高打击分的股票
                max_score_signal = max(result['daily_signals'], key=lambda x: x['strike_score'])  # 修复键名
                print(f"   最高打击分: {max_score_signal['stock_name']} ({max_score_signal['strike_score']:.0f}分)")  # 修复键名

        print(f"\n💡 提示: 如需修改买入信号阈值，请编辑文件头部的 MIN_BUY_SIGNAL_SCORE 配置")
    else:
        print(f"\n❌ 回测失败: {result['reason']}")
        print(f"\n💡 提示:")
        print(f"   请检查数据目录是否存在")
        print(f"   如需修改配置，请编辑文件头部的配置区域")
