import pandas as pd
import os
import re
import sqlite3
from datetime import datetime, time, timedelta
from tabulate import tabulate
import logging
import warnings
import sys
from io import StringIO

# --- 【V7.0 独立实现】核心工具函数 ---
# 为了避免外部依赖问题，直接在此实现必要的工具函数
# 完全参考dynamic_gap_detector.py的文件读取逻辑

def extract_timestamp_from_filename(filename):
    """从文件名中提取时间戳，支持多种格式（完全参考dynamic_gap_detector）"""
    import re

    # 格式1: HH-MM_ (旧格式) 例如: 09-30_zt_pool.csv, 09-30_ths_big_deal.csv
    match = re.search(r'^(\d{2})-(\d{2})_', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    # 格式2: _YYYYMMDD_HHMMSS (新格式) 例如: fund_flow_rank_20250725_093047.csv
    match = re.search(r'_\d{8}_(\d{2})(\d{2})(\d{2})\.', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式3: _HHMMSS.csv (新格式) 例如: 涨停股池_akshare_东方财富_093651.csv
    match = re.search(r'_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式4: limit_up_pool_YYYYMMDD_HHMMSS.csv 例如: limit_up_pool_20250728_093026.csv
    match = re.search(r'limit_up_pool_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式4a: zt_pool_YYYYMMDD_HHMMSS.csv 例如: zt_pool_20250730_093223.csv（新增支持）
    match = re.search(r'zt_pool_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式5: 股票资金流_zssz/zssh_YYYYMMDD_HHMMSS.csv 例如: 股票资金流_zssz_20250728_093050.csv
    match = re.search(r'股票资金流_zs[sh|sz]{2}_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式6: 实时概念资金流_YYYYMMDD_HHMMSS.csv 例如: 实时概念资金流_20250728_093453.csv
    match = re.search(r'实时[概念|板块]资金流_\d{8}_(\d{2})(\d{2})(\d{2})\.csv$', filename)
    if match:
        hour, minute, second = match.groups()
        return f"{hour}{minute}{second}"

    # 格式7: HH-MM_文件名.csv 例如: 09-34_fund_flow_tpdog.csv, 09-34_concept_fund_flow_tpdog.csv
    match = re.search(r'^(\d{2})-(\d{2})_.*\.csv$', filename)
    if match:
        hour, minute = match.groups()
        return f"{hour}{minute}00"

    return None

def classify_file_type(filename):
    """根据文件名分类文件类型，支持多种格式（完全参考dynamic_gap_detector）"""
    # 大单买盘文件匹配
    big_deal_patterns = [
        'ths_big_deal.csv',       # 同花顺大单买盘
        'movers_大笔买入.csv',     # 大笔买入异动
        'big_deal_',              # 大单买盘通用
        'movers_有大买盘'          # 有大买盘异动
    ]

    # 板块异动文件匹配
    board_changes_patterns = [
        'board_changes.csv',      # 板块异动
        'sector_changes.csv',     # 板块变化
        'concept_changes.csv'     # 概念异动
    ]

    # 涨停股池文件匹配（关键修复）
    limit_up_patterns = [
        'limit_up_pool_',         # 涨停股池
        'zt_pool_',              # 涨停池（新增：支持zt_pool_YYYYMMDD_HHMMSS.csv格式）
        'zt_pool.csv',           # 涨停池
        '涨停股池_',              # 涨停股池中文
        'limit_up_'              # 涨停相关
    ]

    # 创月新高指标文件匹配
    new_high_patterns = [
        'indicator_创月新高.csv',  # 创月新高指标
        'new_high_',             # 新高指标
        'monthly_high_',         # 月新高
        'indicator_'             # 指标文件通用
    ]

    # 个股资金流文件匹配（扩展版）
    stock_flow_patterns = [
        'fund_flow_rank_',        # 个股资金流排名
        'fund_flow_tpdog.csv',    # tpdog个股资金流
        'ths_fund_flow.csv',      # 同花顺个股资金流
        'fund_flow_akshare.csv',  # akshare个股资金流
        'individual_fund_flow_',  # 个股资金流排名
        '股票资金流_zssz_',        # 深圳个股资金流
        '股票资金流_zssh_',        # 上海个股资金流
        'stock_fund_flow_'        # 个股资金流通用
    ]

    # 概念资金流文件匹配（扩展版）
    concept_patterns = [
        'concept_fund_flow_tpdog.csv',
        'concept_fund_flow_akshare.csv',
        'concept_fund_flow_',
        '实时概念资金流_',         # 实时概念资金流
        'realtime_concept_flow_'  # 实时概念流
    ]

    # 行业资金流文件匹配（扩展版）
    sector_patterns = [
        'sector_fund_flow_tpdog.csv',
        'sector_fund_flow_akshare.csv',
        'sector_fund_flow_rank_',
        '实时板块资金流_',         # 实时板块资金流
        'realtime_sector_flow_'   # 实时板块流
    ]

    # 按长度降序排序，长模式优先匹配
    all_patterns = []
    for pattern in big_deal_patterns:
        all_patterns.append((pattern, 'big_deal'))
    for pattern in board_changes_patterns:
        all_patterns.append((pattern, 'board_changes'))
    for pattern in limit_up_patterns:
        all_patterns.append((pattern, 'limit_up'))
    for pattern in new_high_patterns:
        all_patterns.append((pattern, 'new_high'))
    for pattern in stock_flow_patterns:
        all_patterns.append((pattern, 'stock_flow'))
    for pattern in concept_patterns:
        all_patterns.append((pattern, 'concept'))
    for pattern in sector_patterns:
        all_patterns.append((pattern, 'sector'))

    all_patterns.sort(key=lambda x: len(x[0]), reverse=True)

    # 逐一匹配模式
    for pattern, file_type in all_patterns:
        if pattern in filename:
            return file_type

    return 'other'

def find_latest_file(file_list, current_time):
    """
    查找最新的文件，确保遵循回测时间限制，不读取未来数据
    【修复】完全参考dynamic_gap_detector.py的实现逻辑
    """
    from datetime import datetime, time

    relevant_file = None
    best_timestamp = None

    # 将current_time转换为time对象
    if isinstance(current_time, str):
        # 如果是字符串格式如"09:31:41"，转换为time对象
        if ':' in current_time:
            current_time = datetime.strptime(current_time, '%H:%M:%S').time()
        else:
            current_time = datetime.strptime(current_time, '%H%M%S').time()

    # 处理所有文件，不要早退出
    for f in sorted(file_list):
        try:
            timestamp = extract_timestamp_from_filename(f)
            if not timestamp:
                continue
            f_ts = datetime.strptime(timestamp, '%H%M%S').time()

            # 核心回测时间验证：确保不读取未来数据
            if f_ts <= current_time:
                # 如果时间戳更晚，或者时间戳相同但文件名更具体（包含日期），则优先选择
                if (best_timestamp is None or
                    f_ts > best_timestamp or
                    (f_ts == best_timestamp and BACKTEST_DATE.replace('-', '') in f and
                     (relevant_file is None or BACKTEST_DATE.replace('-', '') not in relevant_file))):
                    relevant_file = f
                    best_timestamp = f_ts
        except (ValueError, IndexError):
            continue
    return relevant_file

def get_date_folder(date_str, base_dir):
    """获取指定日期的数据文件夹（完全参考dynamic_gap_detector.py的逻辑）"""
    try:
        if not os.path.exists(base_dir):
            print(f"⚠️ 基础数据目录不存在: {base_dir}")
            return None

        # dynamic_gap_detector.py使用原始日期格式：'2025-07-31'
        target_folder = os.path.join(base_dir, date_str)

        if os.path.isdir(target_folder):
            return target_folder

        print(f"⚠️ 未找到日期 {date_str} 对应的文件夹: {target_folder}")
        return None
    except Exception as e:
        print(f"⚠️ 搜索日期文件夹失败: {e}")
        return None

def convert_to_float(value):
    """转换值为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            # 移除常见的非数字字符
            cleaned = value.replace(',', '').replace('%', '').replace('万', '').replace('亿', '')
            return float(cleaned)
        except:
            return 0.0
    return 0.0

def format_amount(amount):
    """格式化金额显示"""
    try:
        amount = float(amount)
        if amount >= 100000000:  # 1亿
            return f"{amount/100000000:.2f}亿"
        elif amount >= 10000:  # 1万
            return f"{amount/10000:.2f}万"
        else:
            return f"{amount:.2f}"
    except:
        return "0"

def get_stock_type(stock_name):
    """获取股票类型（简化实现）"""
    if 'ST' in stock_name:
        return "ST股"
    return "A股"


def parse_limit_up_pool_data(file_path):
    """解析涨停股池文件，返回涨停股票信息（完全参考dynamic_gap_detector.py）"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig', on_bad_lines='skip')

        # 查找可能的列名
        name_columns = ['名称', 'name', '股票名称', '证券名称']
        code_columns = ['代码', 'code', '股票代码', '证券代码']
        price_columns = ['最新价', 'price', '现价', '股价']

        # 新增列名映射（关键：包含连板数）
        additional_columns = {
            '成交额': ['成交额', 'volume', '成交量'],
            '封板资金': ['封板资金', 'seal_amount', '封单金额'],
            '首次封板时间': ['首次封板时间', 'first_seal_time', '首封时间'],
            '最后封板时间': ['最后封板时间', 'last_seal_time', '末封时间'],
            '炸板次数': ['炸板次数', 'break_count', '开板次数'],
            '涨停统计': ['涨停统计', 'limit_up_stat', '涨停次数'],
            '连板数': ['连板数', 'consecutive_days', '连续涨停'],  # 关键字段
            '所属行业': ['所属行业', 'industry', '行业', '板块']
        }

        name_col = next((col for col in name_columns if col in df.columns), None)
        code_col = next((col for col in code_columns if col in df.columns), None)
        price_col = next((col for col in price_columns if col in df.columns), None)

        if name_col:
            result = []
            for _, row in df.iterrows():
                stock_info = {'名称': row[name_col]}
                if code_col and pd.notna(row[code_col]):
                    stock_info['代码'] = row[code_col]
                if price_col and pd.notna(row[price_col]):
                    stock_info['最新价'] = row[price_col]

                # 添加新的字段（特别是连板数）
                for target_col, possible_names in additional_columns.items():
                    for possible_name in possible_names:
                        if possible_name in df.columns and pd.notna(row[possible_name]):
                            stock_info[target_col] = row[possible_name]
                            break

                result.append(stock_info)
            return result
        return []
    except Exception as e:
        print(f"解析涨停股池文件失败: {e}")
        return []



# 导入dynamic_gap_detector的核心股票池分析功能
try:
    from dynamic_gap_detector import (
        get_stock_sectors,
        calculate_sector_leadership_score_v9_4,
        generate_tiered_watchlist_and_report
    )
    DGD_AVAILABLE = True
    print("✅ dynamic_gap_detector 模块导入成功")
except ImportError as e:
    print(f"⚠️ dynamic_gap_detector 导入失败: {e}")
    DGD_AVAILABLE = False

    # 提供备用的简化实现
    def get_stock_sectors(stock_name):
        return {'concepts': [], 'industries': []}

    def calculate_sector_leadership_score_v9_4(all_sectors_df, limit_up_stocks, current_time=None):
        # 简化的板块分析，返回空DataFrame
        return pd.DataFrame()

    def generate_tiered_watchlist_and_report(all_sectors_df_sorted, stock_flow_data, limit_up_stocks):
        # 简化的三梯队分析，返回空结果
        return [], [], [], "备用模式：dynamic_gap_detector不可用"

# 忽略不必要的警告
warnings.filterwarnings("ignore", category=FutureWarning)

# --- 复盘配置 ---
BASE_DATA_DIR = r'D:\dev\mootdx\adata\Gemini\fund_data'
BACKTEST_DATE = '2025-07-31'

# --- 【V7.0 核心升级：资金分配模块】 ---
# 资金分配模块 (Capital Allocation Module)
# 本策略采用**"6-3-1"金字塔式资金分配模型**，将总资金 C_total 划分为三个独立的资金池
TOTAL_CAPITAL = 1000000  # 总资金：100万元

# 第一梯队资金池 (Tier 1 Pool)
TIER1_CAPITAL_RATIO = 0.6  # 资金比例：60%
TIER1_TARGET_POSITIONS = 5  # 目标持仓数：5只股票
TIER1_TOTAL_CAPITAL = TOTAL_CAPITAL * TIER1_CAPITAL_RATIO  # 600,000元
TIER1_CAPITAL_PER_STOCK = TIER1_TOTAL_CAPITAL / TIER1_TARGET_POSITIONS  # 120,000元

# 第二梯队资金池 (Tier 2 Pool)
TIER2_CAPITAL_RATIO = 0.3  # 资金比例：30%
TIER2_TARGET_POSITIONS = 6  # 目标持仓数：6只股票
TIER2_TOTAL_CAPITAL = TOTAL_CAPITAL * TIER2_CAPITAL_RATIO  # 300,000元
TIER2_CAPITAL_PER_STOCK = TIER2_TOTAL_CAPITAL / TIER2_TARGET_POSITIONS  # 50,000元

# 第三梯队资金池 (Tier 3 Pool)
TIER3_CAPITAL_RATIO = 0.1  # 资金比例：10%
TIER3_TARGET_POSITIONS = 1  # 目标持仓数：1只股票
TIER3_TOTAL_CAPITAL = TOTAL_CAPITAL * TIER3_CAPITAL_RATIO  # 100,000元
TIER3_CAPITAL_PER_STOCK = TIER3_TOTAL_CAPITAL / TIER3_TARGET_POSITIONS  # 100,000元

# --- 【V7.0 核心升级：信号捕捉与状态定义】 ---
# 全局状态变量
L_triggered = set()  # 当日已触发过信号的股票列表
L_observe = {}  # 待观察列表：{stock_code: {'time': trigger_time, 'tier': tier}}

# 持仓状态跟踪
tier1_positions = {}  # {stock_code: {'name': name, 'buy_time': time, 'buy_price': price, 'amount': amount}}
tier2_positions = {}
tier3_positions = {}

# 买入信号记录（参考backtestv6.py）
all_buy_signals = []  # 记录所有买入信号，用于最终汇总显示

# 定义开盘和收盘时间
AM_START_TIME = time(9, 15)
PM_END_TIME = time(15, 0)

# --- 【V7.0 核心升级：买入逻辑配置】 ---
# 买入逻辑优先级：第一梯队 > 第二梯队 > 第三梯队
# 第一梯队：最高优先级，支持一字板开板后跟踪买入
# 第二梯队：次高优先级，不支持一字板跟踪（保证资金效率）
# 第三梯队：特殊机会，不支持一字板跟踪

# 全局状态变量
PREVIOUS_SECTOR_DATA_BT = {}
STRONG_SECTORS_LIST_BT = []
MAINLINE_SECTOR_LIST_BT = []
SECTOR_STRENGTH_TRACKER_BT = {}
BREAKOUT_STOCKS_LIST_BT = []
PREVIOUS_RANK_DATA_BT = {}
FIRST_SIGNAL_TIMES_BT = {}
df_analysis_global_bt = pd.DataFrame()

# 日志配置类
class BacktestLogger:
    def __init__(self, backtest_date):
        self.backtest_date = backtest_date
        self.backtest_folder = os.path.join('backtest', backtest_date)
        os.makedirs(self.backtest_folder, exist_ok=True)
        self.full_day_log_file = os.path.join(self.backtest_folder, f'{backtest_date}_full_day.log')
        self.current_hour_log_file = None
        self.current_hour = None
        self.log_buffer = StringIO()
        self.log_format = '%(asctime)s - %(levelname)s - %(message)s'
        self.setup_full_day_logger()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        sys.stdout = LogCapture(self, 'INFO')
        sys.stderr = LogCapture(self, 'ERROR')

    def setup_full_day_logger(self):
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        # 【修复】使用'w'模式覆盖日志文件，确保中文正常显示
        full_day_handler = logging.FileHandler(self.full_day_log_file, mode='w', encoding='utf-8')
        full_day_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(self.log_format)
        full_day_handler.setFormatter(formatter)
        logging.root.setLevel(logging.INFO)
        logging.root.addHandler(full_day_handler)

    def get_hour_from_time(self, current_time):
        if isinstance(current_time, str):
            current_time = datetime.strptime(current_time, '%H:%M:%S').time()
        elif isinstance(current_time, datetime):
            current_time = current_time.time()
        hour = current_time.hour
        minute = current_time.minute
        if hour == 9 and minute >= 30:
            return 9
        elif hour == 10 and minute < 30:
            return 9
        elif hour == 10 and minute >= 30:
            return 10
        elif hour == 11 and minute < 30:
            return 10
        elif hour == 13:
            return 13
        elif hour == 14:
            return 14
        else:
            if minute >= 30:
                return hour
            else:
                return hour - 1 if hour > 0 else 23

    def setup_hourly_logger(self, current_time):
        hour_segment = self.get_hour_from_time(current_time)
        if self.current_hour != hour_segment:
            self.current_hour = hour_segment
            if hour_segment == 9:
                time_range = "0930-1030"
            elif hour_segment == 10:
                time_range = "1030-1130"
            elif hour_segment == 13:
                time_range = "1300-1400"
            elif hour_segment == 14:
                time_range = "1400-1500"
            else:
                time_range = f"{hour_segment:02d}00-{(hour_segment + 1):02d}00"
            self.current_hour_log_file = os.path.join(self.backtest_folder, f'{self.backtest_date}_{time_range}.log')

    def log_message(self, level, message, current_time=None):
        if current_time:
            self.setup_hourly_logger(current_time)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"{timestamp} - {level} - {message}\n"
        if level == 'INFO':
            logging.info(message)
        elif level == 'ERROR':
            logging.error(message)
        elif level == 'WARNING':
            logging.warning(message)
        elif level == 'DEBUG':
            logging.debug(message)
        if self.current_hour_log_file:
            try:
                with open(self.current_hour_log_file, 'a', encoding='utf-8') as f:
                    f.write(formatted_message)
            except Exception as e:
                print(f"写入小时日志失败: {e}")

    def restore_stdout(self):
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr


class LogCapture:
    def __init__(self, logger, level):
        self.logger = logger
        self.level = level
        self.original_stream = sys.stdout if level == 'INFO' else sys.stderr

    def write(self, message):
        self.original_stream.write(message)
        self.original_stream.flush()
        if message.strip():
            self.logger.log_message(self.level, message.strip())

    def flush(self):
        self.original_stream.flush()


# 创建全局日志记录器
backtest_logger = BacktestLogger(BACKTEST_DATE)


def load_sector_data(date_folder, current_time, limit_up_stocks=None):
    """加载板块数据（概念和行业）- 完全参考dynamic_gap_detector.py的逻辑"""
    try:
        all_files = os.listdir(date_folder)

        # 使用文件分类函数筛选文件
        industry_files = []
        concept_files = []

        for f in all_files:
            if f.endswith('.csv'):
                file_type = classify_file_type(f)
                if file_type == 'sector':
                    industry_files.append(f)
                elif file_type == 'concept':
                    concept_files.append(f)

        industry_files = sorted(industry_files)
        concept_files = sorted(concept_files)

        all_sectors_list = []

        # 读取行业文件
        latest_industry_file = find_latest_file(industry_files, current_time)
        if latest_industry_file:
            try:
                df_ind = pd.read_csv(os.path.join(date_folder, latest_industry_file),
                                   encoding='utf-8-sig', on_bad_lines='skip')
                if '名称' in df_ind.columns and '今日主力净流入-净额' in df_ind.columns:
                    df_ind['type'] = '行业'
                    all_sectors_list.append(df_ind[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"⚠️ 读取行业文件失败: {e}")

        # 读取概念文件
        latest_concept_file = find_latest_file(concept_files, current_time)
        if latest_concept_file:
            try:
                df_con = pd.read_csv(os.path.join(date_folder, latest_concept_file),
                                   encoding='utf-8-sig', on_bad_lines='skip')

                # 检查是否需要重命名列（参考dynamic_gap_detector.py的逻辑）
                rename_needed = False
                rename_map = {}

                if '今日主力净流入-净额' not in df_con.columns:
                    for col in df_con.columns:
                        if '净流入' in col and '主力' in col:
                            rename_map[col] = '今日主力净流入-净额'
                            rename_needed = True
                            break

                if rename_needed:
                    df_con = df_con.rename(columns=rename_map)

                if '名称' in df_con.columns and '今日主力净流入-净额' in df_con.columns:
                    df_con['type'] = '概念'
                    all_sectors_list.append(df_con[['名称', '今日主力净流入-净额', 'type']])
            except Exception as e:
                print(f"⚠️ 读取概念文件失败: {e}")

        if all_sectors_list:
            all_sectors_df = pd.concat(all_sectors_list, ignore_index=True)

            # 【【【关键修复：在板块数据源头过滤无意义概念，参考dynamic_gap_detector.py第6000-6017行】】】
            if not all_sectors_df.empty:
                # 使用向量化操作高效过滤概念，保留行业数据
                concept_mask = all_sectors_df['type'] == '概念'
                if concept_mask.any():
                    # 获取无意义概念集合（完全参考dynamic_gap_detector.py）
                    meaningless_items = {
                        '昨日涨停_含一字', '融资融券', '预盈预增', '含可转债', '转债标的',
                        '央视50', '央视50_', '基金重仓', '微盘股', '创业板综', 'QFII重仓',
                        '央国企改革', '专精特新', '标准普尔', '低价股', '昨日涨停', '昨日触板',
                        '一带一路', '高股息精选', '含H股', '通达信88', '含B股', '上证50',
                        '上证50_', '上证180', 'ST板块', '昨日连板_含一字', '昨日连板',
                        '沪股通', '深股通', '港股通', 'MSCI', '标普道琼斯', '富时罗素',
                        '参股新三板', '证金持股', '同花顺漂亮100', '同花顺中特估100',
                        '预亏预减', '机构重仓', '中证500', '深成500', '贬值受益', 'AH股'
                    }
                    # 使用向量化操作过滤概念
                    meaningful_concept_mask = ~all_sectors_df['名称'].isin(meaningless_items)
                    # 保留所有行业数据和有意义的概念数据
                    all_sectors_df = all_sectors_df[~concept_mask | meaningful_concept_mask]

                    print(f"🧹 板块数据清洗完成: 过滤前{len(all_sectors_list[0]) + (len(all_sectors_list[1]) if len(all_sectors_list) > 1 else 0)}个板块，过滤后{len(all_sectors_df)}个板块")

            # 调用龙头评分函数
            if DGD_AVAILABLE:
                try:
                    # 使用传入的涨停股池，如果为None则使用空列表
                    limit_up_list = limit_up_stocks if limit_up_stocks is not None else []
                    print(f"🔍 调用calculate_sector_leadership_score_v9_4，参数: all_sectors_df.shape={all_sectors_df.shape}, limit_up_stocks={len(limit_up_list)}只, current_time={current_time}")
                    print(f"🔍 函数签名检查: {calculate_sector_leadership_score_v9_4.__code__.co_varnames}")
                    all_sectors_df_sorted = calculate_sector_leadership_score_v9_4(
                        all_sectors_df, limit_up_list, current_time=current_time
                    )

                    # 【关键修复】按龙头分从高到低排序，完全参考dynamic_gap_detector.py第6027行
                    if 'leadership_score' in all_sectors_df_sorted.columns:
                        all_sectors_df_sorted = all_sectors_df_sorted.sort_values('leadership_score', ascending=False)

                    print(f"✅ calculate_sector_leadership_score_v9_4调用成功")
                    return all_sectors_df_sorted
                except Exception as e:
                    print(f"❌ calculate_sector_leadership_score_v9_4调用失败: {e}")
                    print(f"❌ 错误类型: {type(e)}")
                    import traceback
                    traceback.print_exc()
                    return pd.DataFrame()
            else:
                return all_sectors_df
        else:
            print(f"⚠️ 未找到板块数据文件")
            return pd.DataFrame()

    except Exception as e:
        print(f"⚠️ 加载板块数据失败: {e}")
        return pd.DataFrame()

# --- 【V7.0 核心升级：三梯队买入信号检测】 ---
def detect_core_pool_signals(current_time, stock_flow_data, all_sectors_df_sorted, limit_up_stocks):
    """
    【核心股票池三梯队分析】买入信号检测

    参数:
    - current_time: 当前时间
    - stock_flow_data: 个股资金流数据
    - all_sectors_df_sorted: 按龙头分排序的板块数据
    - limit_up_stocks: 涨停股池数据

    返回:
    - dict: {'tier1': [], 'tier2': [], 'tier3': []} 三梯队买入信号
    """
    global L_triggered, L_observe

    print(f"\n🎯 【{current_time}】开始核心股票池三梯队分析...")

    # 检查dynamic_gap_detector是否可用
    if not DGD_AVAILABLE:
        print("⚠️ dynamic_gap_detector不可用，使用备用模式")
        return {'tier1': [], 'tier2': [], 'tier3': []}

    # 调用dynamic_gap_detector的核心分析函数
    try:
        tier1_stocks, tier2_stocks, tier3_stocks, report_content = generate_tiered_watchlist_and_report(
            all_sectors_df_sorted, stock_flow_data, limit_up_stocks
        )

        # 信号捕捉逻辑：首次出现检测
        new_signals = {'tier1': [], 'tier2': [], 'tier3': []}

        # 第一梯队信号检测
        for stock in tier1_stocks:
            stock_name = stock['name']
            if stock_name not in L_triggered:
                # 【修复】记录所有信号，包括一字板（标注不可买入）
                is_tradable = is_tradable_stock(stock_name, stock_flow_data, tier='tier1', limit_up_stocks=limit_up_stocks)
                stock['is_tradable'] = is_tradable  # 添加可交易标记
                new_signals['tier1'].append(stock)
                L_triggered.add(stock_name)
                L_observe[stock_name] = {
                    'time': current_time,
                    'tier': 'tier1',
                    'score': stock['total_score'],
                    'rank': stock['rank'],
                    'is_tradable': is_tradable
                }
                if is_tradable:
                    print(f"🥇 第一梯队新信号: {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']})")
                else:
                    print(f"🔒 第一梯队信号(一字板): {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']}) - 暂不买入")

                # 【修复】记录所有信号到汇总列表（包括一字板）
                record_signal_to_summary(stock, 'tier1', current_time, is_tradable, stock_flow_data)

        # 第二梯队信号检测
        for stock in tier2_stocks:
            stock_name = stock['name']
            if stock_name not in L_triggered:
                # 【修复】记录所有信号，包括一字板（标注不可买入）
                is_tradable = is_tradable_stock(stock_name, stock_flow_data, tier='tier2', limit_up_stocks=limit_up_stocks)
                stock['is_tradable'] = is_tradable  # 添加可交易标记
                new_signals['tier2'].append(stock)
                L_triggered.add(stock_name)
                L_observe[stock_name] = {
                    'time': current_time,
                    'tier': 'tier2',
                    'score': stock['total_score'],
                    'rank': stock['rank'],
                    'is_tradable': is_tradable
                }
                if is_tradable:
                    print(f"🥈 第二梯队新信号: {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']})")
                else:
                    print(f"🔒 第二梯队信号(一字板): {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']}) - 不支持一字板跟踪")

                # 【修复】记录所有信号到汇总列表（包括一字板）
                record_signal_to_summary(stock, 'tier2', current_time, is_tradable, stock_flow_data)

        # 第三梯队信号检测
        for stock in tier3_stocks:
            stock_name = stock['name']
            if stock_name not in L_triggered:
                # 【修复】记录所有信号，包括一字板（标注不可买入）
                is_tradable = is_tradable_stock(stock_name, stock_flow_data, tier='tier3', limit_up_stocks=limit_up_stocks)
                stock['is_tradable'] = is_tradable  # 添加可交易标记
                new_signals['tier3'].append(stock)
                L_triggered.add(stock_name)
                L_observe[stock_name] = {
                    'time': current_time,
                    'tier': 'tier3',
                    'score': stock['total_score'],
                    'rank': stock['rank'],
                    'is_tradable': is_tradable
                }
                if is_tradable:
                    print(f"🥉 第三梯队新信号: {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']})")
                else:
                    print(f"🔒 第三梯队信号(一字板): {stock_name} (联动分:{stock['total_score']:.2f}, 排名:{stock['rank']}) - 不支持一字板跟踪")

                # 【修复】记录所有信号到汇总列表（包括一字板）
                record_signal_to_summary(stock, 'tier3', current_time, is_tradable, stock_flow_data)

        # 打印信号汇总
        total_new_signals = len(new_signals['tier1']) + len(new_signals['tier2']) + len(new_signals['tier3'])
        print(f"✅ 本次新增信号: 第一梯队{len(new_signals['tier1'])}只, 第二梯队{len(new_signals['tier2'])}只, 第三梯队{len(new_signals['tier3'])}只, 共{total_new_signals}只")

        return new_signals

    except Exception as e:
        print(f"⚠️ 核心股票池三梯队分析失败: {e}")
        return {'tier1': [], 'tier2': [], 'tier3': []}


def record_signal_to_summary(stock, tier, current_time, is_tradable, stock_flow_data=None):
    """
    记录信号到汇总列表（包括一字板信号）

    参数:
    - stock: 股票信息
    - tier: 梯队类型
    - current_time: 当前时间
    - is_tradable: 是否可交易
    - stock_flow_data: 个股资金流数据（用于获取真实价格）
    """
    try:
        # 获取股票基本信息
        stock_name = stock['name']

        # 【修复】从stock_flow_data中获取真实价格
        price = 10.0  # 默认价格
        if stock_flow_data is not None:
            stock_info = stock_flow_data[stock_flow_data['名称'] == stock_name]
            if not stock_info.empty:
                real_price = stock_info.iloc[0].get('最新价', 0)
                if isinstance(real_price, (int, float)) and real_price > 0:
                    price = real_price

        # 计算股数和成本
        if is_tradable:
            # 根据梯队确定买入金额
            if tier == 'tier1':
                buy_amount = TIER1_CAPITAL_PER_STOCK
            elif tier == 'tier2':
                buy_amount = TIER2_CAPITAL_PER_STOCK
            elif tier == 'tier3':
                buy_amount = TIER3_CAPITAL_PER_STOCK
            else:
                buy_amount = 50000  # 默认5万

            shares = int(buy_amount / price)
            cost = shares * price
        else:
            shares = 0  # 一字板股票股数为0
            cost = 0

        # 记录信号信息
        signal_info = {
            'Time': f"{BACKTEST_DATE} {current_time}",
            'Stock_Name': stock_name,
            'Price': price,
            'Shares': shares,
            'Cost': cost,
            'Tier': tier,
            'Synergy_Score': stock['total_score'],
            'Rank': stock['rank'],
            'Sector': stock['sector'],
            'Signal_Level': tier,
            'Is_Tradable': is_tradable
        }
        all_buy_signals.append(signal_info)

    except Exception as e:
        print(f"⚠️ 记录信号失败: {e}")


def is_tradable_stock(stock_name, stock_flow_data, tier='tier1', limit_up_stocks=None):
    """
    检查股票是否可交易（避免一字板等不可交易情况）
    【修复】使用涨停股池数据正确判断一字板

    参数:
    - stock_name: 股票名称
    - stock_flow_data: 个股资金流数据
    - tier: 梯队类型 ('tier1', 'tier2', 'tier3')
    - limit_up_stocks: 涨停股池数据

    返回:
    - bool: 是否可交易
    """
    try:
        # 从资金流数据中查找股票信息
        stock_info = stock_flow_data[stock_flow_data['名称'] == stock_name]
        if stock_info.empty:
            print(f"⚠️ 未找到股票 {stock_name} 的资金流数据")
            return False

        stock_row = stock_info.iloc[0]

        # 获取涨跌幅
        change_pct = stock_row.get('今日涨跌幅', 0)
        if not isinstance(change_pct, (int, float)):
            change_pct = 0

        # 获取成交额
        turnover = stock_row.get('今日成交额', 0)
        if not isinstance(turnover, (int, float)):
            turnover = 0

        # 【修复】使用涨停股池数据判断一字板
        is_one_word_board = check_one_word_board(stock_name, limit_up_stocks, change_pct, turnover)

        # 第一梯队：支持一字板开板后跟踪买入
        if tier == 'tier1':
            if is_one_word_board:
                print(f"🔒 {stock_name} 确认一字板，第一梯队暂不买入，等待开板")
                return False
            return True

        # 第二、三梯队：不支持一字板跟踪
        else:
            if is_one_word_board:
                print(f"🔒 {stock_name} 确认一字板，{tier}不支持一字板跟踪")
                return False

            # ST股票过滤
            if 'ST' in stock_name or '*ST' in stock_name:
                print(f"🔒 {stock_name} 为ST股票，{tier}不买入")
                return False

            return True

    except Exception as e:
        print(f"⚠️ 检查股票 {stock_name} 可交易性失败: {e}")
        return False


def check_one_word_board(stock_name, limit_up_stocks, change_pct, turnover):
    """
    检查是否为一字板

    参数:
    - stock_name: 股票名称
    - limit_up_stocks: 涨停股池数据
    - change_pct: 涨跌幅
    - turnover: 成交额

    返回:
    - bool: 是否为一字板
    """
    try:
        # 如果没有涨停股池数据，使用简化判断
        if not limit_up_stocks:
            # 涨幅接近10%且成交额极低（小于1000万）
            return change_pct >= 9.8 and turnover < 10000000

        # 在涨停股池中查找该股票
        limit_up_info = None
        for stock in limit_up_stocks:
            if stock.get('名称') == stock_name:
                limit_up_info = stock
                break

        # 如果不在涨停股池中，不是一字板
        if not limit_up_info:
            return False

        # 检查一字板特征
        # 1. 炸板次数为0（没有开过板）
        break_count = limit_up_info.get('炸板次数', 1)
        if isinstance(break_count, (int, float)) and break_count == 0:
            # 2. 首次封板时间很早（09:30之前，即集合竞价）
            first_seal_time = limit_up_info.get('首次封板时间', '')
            if first_seal_time:
                time_str = str(first_seal_time).strip()
                # 处理不同时间格式
                if len(time_str) == 6 and time_str.isdigit():  # 092500格式
                    time_int = int(time_str)
                    if time_int <= 93000:  # 09:30:00之前
                        return True
                elif len(time_str) == 5 and time_str.isdigit():  # 92500格式
                    time_int = int(f"0{time_str}")
                    if time_int <= 93000:
                        return True
                elif time_str <= "09:30:00":  # HH:MM:SS格式
                    return True

        # 3. 备用判断：成交额极低
        stock_turnover = limit_up_info.get('成交额', turnover)
        if isinstance(stock_turnover, (int, float)) and stock_turnover < 5000000:  # 小于500万
            return True

        return False

    except Exception as e:
        print(f"⚠️ 检查一字板失败: {e}")
        # 发生异常时使用保守判断
        return change_pct >= 9.9 and turnover < 5000000


def execute_buy_orders(new_signals, current_time, stock_flow_data):
    """
    执行买入订单（按梯队优先级）

    参数:
    - new_signals: 新信号字典 {'tier1': [], 'tier2': [], 'tier3': []}
    - current_time: 当前时间
    - stock_flow_data: 个股资金流数据

    返回:
    - dict: 执行结果统计
    """
    global tier1_positions, tier2_positions, tier3_positions

    execution_stats = {
        'tier1_bought': 0,
        'tier2_bought': 0,
        'tier3_bought': 0,
        'total_cost': 0
    }

    print(f"\n💰 【{current_time}】开始执行买入订单...")

    # 第一梯队买入（最高优先级）
    for stock in new_signals['tier1']:
        if len(tier1_positions) >= TIER1_TARGET_POSITIONS:
            print(f"🔒 第一梯队已满仓 ({len(tier1_positions)}/{TIER1_TARGET_POSITIONS}只)，跳过 {stock['name']}")
            continue

        # 【修复】只对可交易股票执行买入
        if not stock.get('is_tradable', True):
            print(f"🔒 跳过一字板股票: {stock['name']} (第一梯队)")
            continue

        success, cost = execute_single_buy_order(stock, 'tier1', current_time, stock_flow_data)
        if success:
            execution_stats['tier1_bought'] += 1
            execution_stats['total_cost'] += cost

    # 第二梯队买入
    for stock in new_signals['tier2']:
        if len(tier2_positions) >= TIER2_TARGET_POSITIONS:
            print(f"🔒 第二梯队已满仓 ({len(tier2_positions)}/{TIER2_TARGET_POSITIONS}只)，跳过 {stock['name']}")
            continue

        # 【修复】只对可交易股票执行买入
        if not stock.get('is_tradable', True):
            print(f"🔒 跳过一字板股票: {stock['name']} (第二梯队)")
            continue

        success, cost = execute_single_buy_order(stock, 'tier2', current_time, stock_flow_data)
        if success:
            execution_stats['tier2_bought'] += 1
            execution_stats['total_cost'] += cost

    # 第三梯队买入
    for stock in new_signals['tier3']:
        if len(tier3_positions) >= TIER3_TARGET_POSITIONS:
            print(f"🔒 第三梯队已满仓 ({len(tier3_positions)}/{TIER3_TARGET_POSITIONS}只)，跳过 {stock['name']}")
            continue

        # 【修复】只对可交易股票执行买入
        if not stock.get('is_tradable', True):
            print(f"🔒 跳过一字板股票: {stock['name']} (第三梯队)")
            continue

        success, cost = execute_single_buy_order(stock, 'tier3', current_time, stock_flow_data)
        if success:
            execution_stats['tier3_bought'] += 1
            execution_stats['total_cost'] += cost

    # 打印执行结果
    total_bought = execution_stats['tier1_bought'] + execution_stats['tier2_bought'] + execution_stats['tier3_bought']
    print(f"✅ 买入执行完成: 第一梯队{execution_stats['tier1_bought']}只, 第二梯队{execution_stats['tier2_bought']}只, 第三梯队{execution_stats['tier3_bought']}只")
    print(f"💸 总成本: {format_amount(execution_stats['total_cost'])}")

    return execution_stats


def execute_single_buy_order(stock, tier, current_time, stock_flow_data):
    """
    执行单个股票的买入订单

    参数:
    - stock: 股票信息字典
    - tier: 梯队类型 ('tier1', 'tier2', 'tier3')
    - current_time: 当前时间
    - stock_flow_data: 个股资金流数据

    返回:
    - tuple: (是否成功, 成本)
    """
    global tier1_positions, tier2_positions, tier3_positions, all_buy_signals

    stock_name = stock['name']

    try:
        # 获取股票当前价格（使用最新价作为买入价格）
        stock_info = stock_flow_data[stock_flow_data['名称'] == stock_name]
        if stock_info.empty:
            print(f"⚠️ 无法获取 {stock_name} 的价格信息")
            return False, 0

        current_price = stock_info.iloc[0].get('最新价', 0)
        if not isinstance(current_price, (int, float)) or current_price <= 0:
            print(f"⚠️ {stock_name} 价格异常: {current_price}")
            return False, 0

        # 根据梯队确定买入金额
        if tier == 'tier1':
            buy_amount = TIER1_CAPITAL_PER_STOCK
            positions_dict = tier1_positions
        elif tier == 'tier2':
            buy_amount = TIER2_CAPITAL_PER_STOCK
            positions_dict = tier2_positions
        elif tier == 'tier3':
            buy_amount = TIER3_CAPITAL_PER_STOCK
            positions_dict = tier3_positions
        else:
            print(f"⚠️ 未知梯队类型: {tier}")
            return False, 0

        # 计算买入股数（100股为单位）
        shares = int(buy_amount / current_price / 100) * 100
        if shares < 100:
            print(f"⚠️ {stock_name} 价格过高，无法买入100股 (价格:{current_price}, 可用资金:{format_amount(buy_amount)})")
            return False, 0

        # 计算实际成本
        actual_cost = shares * current_price

        # 记录持仓
        positions_dict[stock_name] = {
            'name': stock_name,
            'buy_time': current_time,
            'buy_price': current_price,
            'shares': shares,
            'cost': actual_cost,
            'tier': tier,
            'synergy_score': stock['total_score'],
            'rank': stock['rank'],
            'sector': stock['sector']
        }

        # 打印买入信息
        tier_emoji = {'tier1': '🥇', 'tier2': '🥈', 'tier3': '🥉'}
        print(f"{tier_emoji[tier]} 买入 {stock_name}: {shares}股 × {current_price:.2f}元 = {format_amount(actual_cost)} ({tier})")
        print(f"   联动分:{stock['total_score']:.2f}, 资金排名:{stock['rank']}, 板块:{stock['sector']}")

        # 【修复】更新已记录信号的实际买入信息
        for signal in all_buy_signals:
            if (signal['Stock_Name'] == stock_name and
                signal['Time'] == f"{BACKTEST_DATE} {current_time}" and
                signal['Tier'] == tier):
                signal['Price'] = current_price
                signal['Shares'] = shares
                signal['Cost'] = actual_cost
                break

        return True, actual_cost

    except Exception as e:
        print(f"⚠️ 买入 {stock_name} 失败: {e}")
        return False, 0


def print_positions_summary():
    """打印当前持仓汇总"""
    global tier1_positions, tier2_positions, tier3_positions

    print(f"\n📊 【当前持仓汇总】")
    print("=" * 80)

    total_cost = 0
    total_positions = 0

    # 第一梯队持仓
    if tier1_positions:
        print(f"\n🥇 第一梯队持仓 ({len(tier1_positions)}/{TIER1_TARGET_POSITIONS}只):")
        tier1_table = []
        tier1_cost = 0
        for stock_name, pos in tier1_positions.items():
            tier1_table.append([
                stock_name,
                f"{pos['synergy_score']:.2f}",
                pos['rank'],
                pos['sector'],
                pos['shares'],
                f"{pos['buy_price']:.2f}",
                format_amount(pos['cost']),
                pos['buy_time']
            ])
            tier1_cost += pos['cost']

        print(tabulate(tier1_table,
                      headers=['股票名称', '联动分', '资金排名', '所属板块', '股数', '买入价', '成本', '买入时间'],
                      tablefmt='psql', showindex=False))
        print(f"   第一梯队总成本: {format_amount(tier1_cost)}")
        total_cost += tier1_cost
        total_positions += len(tier1_positions)
    else:
        print(f"\n🥇 第一梯队持仓 (0/{TIER1_TARGET_POSITIONS}只): 暂无持仓")

    # 第二梯队持仓
    if tier2_positions:
        print(f"\n🥈 第二梯队持仓 ({len(tier2_positions)}/{TIER2_TARGET_POSITIONS}只):")
        tier2_table = []
        tier2_cost = 0
        for stock_name, pos in tier2_positions.items():
            tier2_table.append([
                stock_name,
                f"{pos['synergy_score']:.2f}",
                pos['rank'],
                pos['sector'],
                pos['shares'],
                f"{pos['buy_price']:.2f}",
                format_amount(pos['cost']),
                pos['buy_time']
            ])
            tier2_cost += pos['cost']

        print(tabulate(tier2_table,
                      headers=['股票名称', '联动分', '资金排名', '所属板块', '股数', '买入价', '成本', '买入时间'],
                      tablefmt='psql', showindex=False))
        print(f"   第二梯队总成本: {format_amount(tier2_cost)}")
        total_cost += tier2_cost
        total_positions += len(tier2_positions)
    else:
        print(f"\n🥈 第二梯队持仓 (0/{TIER2_TARGET_POSITIONS}只): 暂无持仓")

    # 第三梯队持仓
    if tier3_positions:
        print(f"\n🥉 第三梯队持仓 ({len(tier3_positions)}/{TIER3_TARGET_POSITIONS}只):")
        tier3_table = []
        tier3_cost = 0
        for stock_name, pos in tier3_positions.items():
            tier3_table.append([
                stock_name,
                f"{pos['synergy_score']:.2f}",
                pos['rank'],
                pos['sector'],
                pos['shares'],
                f"{pos['buy_price']:.2f}",
                format_amount(pos['cost']),
                pos['buy_time']
            ])
            tier3_cost += pos['cost']

        print(tabulate(tier3_table,
                      headers=['股票名称', '联动分', '资金排名', '所属板块', '股数', '买入价', '成本', '买入时间'],
                      tablefmt='psql', showindex=False))
        print(f"   第三梯队总成本: {format_amount(tier3_cost)}")
        total_cost += tier3_cost
        total_positions += len(tier3_positions)
    else:
        print(f"\n🥉 第三梯队持仓 (0/{TIER3_TARGET_POSITIONS}只): 暂无持仓")

    # 总计
    max_positions = TIER1_TARGET_POSITIONS + TIER2_TARGET_POSITIONS + TIER3_TARGET_POSITIONS
    remaining_capital = TOTAL_CAPITAL - total_cost
    print(f"\n💰 总持仓: {total_positions}/{max_positions}只, 总成本: {format_amount(total_cost)}")
    print(f"💰 剩余资金: {format_amount(remaining_capital)} ({remaining_capital/TOTAL_CAPITAL*100:.1f}%)")
    print("=" * 80)


# --- 【V7.0 核心升级：主回测函数】 ---
def run_backtest_v7():
    """
    V7.0 回测主函数：基于核心股票池三梯队分析的买入信号
    """
    global L_triggered, L_observe, tier1_positions, tier2_positions, tier3_positions

    print(f"\n🚀 【BackTest V7.0】开始回测 - 日期: {BACKTEST_DATE}")
    print(f"🎯 策略: 核心股票池三梯队分析买入信号")
    print(f"💰 总资金: {format_amount(TOTAL_CAPITAL)} (第一梯队:{TIER1_CAPITAL_RATIO*100:.0f}%, 第二梯队:{TIER2_CAPITAL_RATIO*100:.0f}%, 第三梯队:{TIER3_CAPITAL_RATIO*100:.0f}%)")
    print("=" * 100)

    # 获取数据目录
    date_folder = get_date_folder(BACKTEST_DATE, BASE_DATA_DIR)
    if not date_folder:
        print(f"❌ 未找到日期 {BACKTEST_DATE} 的数据文件夹")
        return

    print(f"📁 数据目录: {date_folder}")

    # 获取所有时间点的数据文件（完全参考dynamic_gap_detector的逻辑）
    all_files = os.listdir(date_folder)

    # 使用文件分类函数筛选各类文件
    stock_flow_files = []
    limit_up_files = []
    concept_files = []
    sector_files = []

    for f in all_files:
        if f.endswith('.csv'):
            file_type = classify_file_type(f)
            if file_type == 'stock_flow':
                stock_flow_files.append(f)
            elif file_type == 'limit_up':
                limit_up_files.append(f)
            elif file_type == 'concept':
                concept_files.append(f)
            elif file_type == 'sector':
                sector_files.append(f)

    stock_flow_files = sorted(stock_flow_files)
    limit_up_files = sorted(limit_up_files)
    concept_files = sorted(concept_files)
    sector_files = sorted(sector_files)

    print(f"📊 文件扫描结果: 个股资金流{len(stock_flow_files)}个, 涨停股池{len(limit_up_files)}个, 概念{len(concept_files)}个, 行业{len(sector_files)}个")

    # 提取所有时间戳
    timestamps = set()
    for f in stock_flow_files:
        timestamp = extract_timestamp_from_filename(f)
        if timestamp:
            timestamps.add(timestamp)

    # 按时间戳排序
    sorted_timestamps = sorted(timestamps)
    print(f"📊 找到 {len(sorted_timestamps)} 个时间点")

    if not sorted_timestamps:
        print("❌ 未找到任何时间点数据文件")
        return

    # 逐个时间点进行回测
    for i, timestamp_str in enumerate(sorted_timestamps):
        # 将字符串时间戳转换为datetime.time对象
        from datetime import time
        hour = int(timestamp_str[:2])
        minute = int(timestamp_str[2:4])
        second = int(timestamp_str[4:6]) if len(timestamp_str) >= 6 else 0
        current_time = time(hour, minute, second)

        # 跳过非交易时间
        if not is_trading_time(current_time):
            continue

        # 查找当前时间点的最新个股资金流文件
        latest_stock_flow_file = find_latest_file(stock_flow_files, current_time)
        if not latest_stock_flow_file:
            print(f"⚠️ {current_time} 未找到个股资金流文件")
            continue

        # 格式化时间显示
        current_time_str = current_time.strftime('%H:%M:%S')
        print(f"\n⏰ 【{current_time_str}】处理第 {i+1}/{len(sorted_timestamps)} 个时间点 (文件: {latest_stock_flow_file})")

        try:
            # 加载当前时间点的数据
            stock_flow_data, all_sectors_df_sorted, limit_up_stocks = load_time_point_data(
                date_folder, latest_stock_flow_file, current_time_str
            )

            if stock_flow_data is None or all_sectors_df_sorted is None:
                print(f"⚠️ {current_time_str} 数据加载失败，跳过")
                continue

            # 检测核心股票池三梯队买入信号
            new_signals = detect_core_pool_signals(
                current_time_str, stock_flow_data, all_sectors_df_sorted, limit_up_stocks
            )

            # 执行买入订单
            if any(new_signals.values()):  # 如果有新信号
                execution_stats = execute_buy_orders(new_signals, current_time_str, stock_flow_data)

                # 打印持仓汇总（每次有交易时）
                if execution_stats['tier1_bought'] + execution_stats['tier2_bought'] + execution_stats['tier3_bought'] > 0:
                    print_positions_summary()

            # 每小时打印一次状态（整点时间）
            if current_time.minute == 0:
                print_hourly_status(current_time_str)

        except Exception as e:
            print(f"⚠️ 处理时间点 {current_time_str} 时发生错误: {e}")
            continue

    # 回测结束，打印最终结果
    print_final_backtest_results()


def is_trading_time(time_obj):
    """判断是否为交易时间"""
    # 上午: 9:30-11:30
    if time(9, 30) <= time_obj <= time(11, 30):
        return True
    # 下午: 13:00-15:00
    if time(13, 0) <= time_obj <= time(15, 0):
        return True
    return False


def load_time_point_data(date_folder, time_file, current_time):
    """
    加载指定时间点的数据（修正：正确加载涨停股池文件）

    参数:
    - date_folder: 日期文件夹路径
    - time_file: 时间文件名
    - current_time: 当前时间字符串

    返回:
    - tuple: (stock_flow_data, all_sectors_df_sorted, limit_up_stocks)
    """
    try:
        # 加载个股资金流数据
        file_path = os.path.join(date_folder, time_file)
        stock_flow_data = pd.read_csv(file_path, encoding='utf-8')

        if stock_flow_data.empty:
            print(f"⚠️ {current_time} 个股资金流数据为空")
            return None, None, None

        print(f"📊 加载个股数据: {len(stock_flow_data)} 只股票")

        # 【关键修复】：从真正的涨停股池文件中加载涨停股数据
        limit_up_stocks = load_limit_up_stocks_from_files(date_folder, current_time)
        # print(f"📊 涨停股数据: {len(limit_up_stocks)} 只涨停股 (来源: 涨停股池文件)")  # 调试信息已移除

        # 如果没有找到涨停股池文件，则使用备用方法
        if not limit_up_stocks:
            print("⚠️ 未找到涨停股池文件，使用个股资金流数据模拟")
            limit_up_stocks = get_limit_up_stocks_from_flow_data(stock_flow_data)
            print(f"📊 备用涨停股数据: {len(limit_up_stocks)} 只涨停股 (来源: 个股资金流)")

        # 加载板块数据（概念和行业）- 传入涨停股池
        all_sectors_df_sorted = load_sector_data(date_folder, current_time, limit_up_stocks)

        if all_sectors_df_sorted is None or all_sectors_df_sorted.empty:
            print(f"⚠️ {current_time} 板块龙头分数据为空")
            return stock_flow_data, None, limit_up_stocks

        print(f"📊 计算板块数据: {len(all_sectors_df_sorted)} 个板块")

        return stock_flow_data, all_sectors_df_sorted, limit_up_stocks

    except Exception as e:
        print(f"⚠️ 加载 {current_time} 数据失败: {e}")
        return None, None, None


def load_limit_up_stocks_from_files(date_folder, current_time):
    """从涨停股池文件中加载涨停股数据（包含连板数等关键信息）"""
    try:
        all_files = os.listdir(date_folder)

        # 使用文件分类函数筛选涨停股池文件
        limit_up_files = []
        for f in all_files:
            if f.endswith('.csv'):
                file_type = classify_file_type(f)
                if file_type == 'limit_up':
                    limit_up_files.append(f)

        limit_up_files = sorted(limit_up_files)
        # print(f"🔍 找到 {len(limit_up_files)} 个涨停股池文件")  # 调试信息已移除

        # 查找当前时间点的最新涨停股池文件
        latest_limit_up_file = find_latest_file(limit_up_files, current_time)
        if latest_limit_up_file:
            # print(f"📁 使用涨停股池文件: {latest_limit_up_file}")  # 调试信息已移除
            file_path = os.path.join(date_folder, latest_limit_up_file)
            limit_up_stocks = parse_limit_up_pool_data(file_path)

            # 验证连板数数据
            has_consecutive_data = any(stock.get('连板数') is not None for stock in limit_up_stocks)
            if has_consecutive_data:
                print(f"✅ 涨停股池包含连板数数据，共 {len(limit_up_stocks)} 只股票")
            else:
                print(f"⚠️ 涨停股池缺少连板数数据，共 {len(limit_up_stocks)} 只股票")

            return limit_up_stocks
        else:
            print("⚠️ 未找到符合时间要求的涨停股池文件")
            return []

    except Exception as e:
        print(f"⚠️ 从涨停股池文件加载数据失败: {e}")
        return []


def get_limit_up_stocks_from_flow_data(stock_flow_data):
    """从个股资金流数据中提取涨停股信息（备用方法，缺少连板数）"""
    try:
        limit_up_stocks = []

        for _, row in stock_flow_data.iterrows():
            change_pct = row.get('今日涨跌幅', 0)
            if isinstance(change_pct, (int, float)) and change_pct >= 9.8:
                limit_up_stocks.append({
                    '名称': row.get('名称', ''),
                    '涨跌幅': change_pct,
                    '成交额': row.get('今日成交额', 0),
                    '净流入': row.get('今日主力净流入-净额', 0),
                    '连板数': 1  # 备用方法：默认连板数为1（无法获取真实连板数）
                })

        return limit_up_stocks

    except Exception as e:
        print(f"⚠️ 从个股资金流提取涨停股数据失败: {e}")
        return []


# 保留原函数名以兼容现有代码
def get_limit_up_stocks(stock_flow_data):
    """从个股数据中提取涨停股信息（兼容性函数）"""
    return get_limit_up_stocks_from_flow_data(stock_flow_data)


def print_hourly_status(current_time):
    """打印每小时状态汇总"""
    global L_triggered, L_observe

    print(f"\n⏰ 【{current_time}】整点状态汇总")
    print("-" * 60)
    print(f"🎯 累计触发信号: {len(L_triggered)} 只股票")
    print(f"👀 待观察列表: {len(L_observe)} 只股票")

    # 按梯队统计持仓
    tier1_count = len(tier1_positions)
    tier2_count = len(tier2_positions)
    tier3_count = len(tier3_positions)
    total_positions = tier1_count + tier2_count + tier3_count

    print(f"📊 当前持仓: 第一梯队{tier1_count}只, 第二梯队{tier2_count}只, 第三梯队{tier3_count}只, 共{total_positions}只")
    print("-" * 60)


def print_all_buy_signals_summary():
    """显示当天所有买入信号汇总（参考backtestv6.py）"""
    global all_buy_signals

    if not all_buy_signals:
        print("\n--- 复盘完成，未触发任何买入信号 ---")
        return

    print(f"\n" + "=" * 80 + " 当天买入信号汇总 " + "=" * 80)
    print(f"📊 买入信号总数: {len(all_buy_signals)} 个")

    # 按梯队统计
    tier_stats = {}
    for signal in all_buy_signals:
        tier = signal['Tier']
        tier_stats[tier] = tier_stats.get(tier, 0) + 1

    print(f"梯队分布: ", end="")
    for tier in ['tier1', 'tier2', 'tier3']:
        if tier in tier_stats:
            tier_name = {'tier1': '第一梯队', 'tier2': '第二梯队', 'tier3': '第三梯队'}[tier]
            print(f"{tier_name}{tier_stats[tier]}个 ", end="")
    print()

    # 按时间排序显示所有买入信号
    all_buy_signals.sort(key=lambda x: x['Time'])

    print(f"\n📋 买入信号详情 (按时间排序):")
    print("-" * 120)

    for i, signal in enumerate(all_buy_signals, 1):
        tier_name = {'tier1': '第一梯队', 'tier2': '第二梯队', 'tier3': '第三梯队'}[signal['Tier']]
        time_str = signal['Time'].split(' ')[1] if ' ' in signal['Time'] else signal['Time']

        # 【修复】显示一字板状态
        tradable_status = "" if signal.get('Is_Tradable', True) else " [一字板-未买入]"

        print(f"{i:2d}. [{time_str}] {signal['Stock_Name']} - {tier_name}{tradable_status}")
        print(f"    价格: {signal['Price']:.2f}元 | 股数: {signal['Shares']}股 | 成本: {format_amount(signal['Cost'])}")
        print(f"    联动分: {signal['Synergy_Score']:.2f} | 资金排名: {signal['Rank']} | 板块: {signal['Sector']}")
        print()

    # 生成并保存买入信号报告（避免重复写入）
    save_buy_signals_report()

    print("=" * 180)


def save_buy_signals_report():
    """保存买入信号报告（避免重复写入）"""
    global all_buy_signals

    if not all_buy_signals:
        return

    # 确保目录存在
    report_dir = f"backtest/{BACKTEST_DATE}"
    os.makedirs(report_dir, exist_ok=True)

    # 报告文件路径（覆盖模式，避免重复写入）
    report_file = os.path.join(report_dir, f"buy_signals_summary_{BACKTEST_DATE.replace('-', '')}.txt")

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"====== {BACKTEST_DATE} 三梯队买入信号报告 ======\n\n")
            f.write(f"回测系统: BackTest V7.0 - 核心股票池三梯队分析\n")
            f.write(f"信号总数: {len(all_buy_signals)} 个\n\n")

            # 梯队统计
            tier_stats = {}
            for signal in all_buy_signals:
                tier = signal['Tier']
                tier_stats[tier] = tier_stats.get(tier, 0) + 1

            f.write("梯队分布统计:\n")
            for tier in ['tier1', 'tier2', 'tier3']:
                if tier in tier_stats:
                    tier_name = {'tier1': '第一梯队', 'tier2': '第二梯队', 'tier3': '第三梯队'}[tier]
                    f.write(f"  {tier_name}: {tier_stats[tier]} 个信号\n")
            f.write("\n")

            # 详细信号列表
            f.write("买入信号详情 (按时间排序):\n")
            f.write("-" * 100 + "\n")

            for i, signal in enumerate(all_buy_signals, 1):
                tier_name = {'tier1': '第一梯队', 'tier2': '第二梯队', 'tier3': '第三梯队'}[signal['Tier']]
                time_str = signal['Time'].split(' ')[1] if ' ' in signal['Time'] else signal['Time']

                # 【修复】显示一字板状态
                tradable_status = "" if signal.get('Is_Tradable', True) else " [一字板-未买入]"

                f.write(f"{i:2d}. [{time_str}] {signal['Stock_Name']} - {tier_name}{tradable_status}\n")
                f.write(f"    价格: {signal['Price']:.2f}元 | 股数: {signal['Shares']}股 | 成本: {format_amount(signal['Cost'])}\n")
                f.write(f"    联动分: {signal['Synergy_Score']:.2f} | 资金排名: {signal['Rank']} | 板块: {signal['Sector']}\n\n")

        print(f"📄 买入信号报告已保存至: {report_file}")

    except Exception as e:
        print(f"⚠️ 保存买入信号报告失败: {e}")


def print_final_backtest_results():
    """打印最终回测结果"""
    global L_triggered, L_observe, tier1_positions, tier2_positions, tier3_positions

    print(f"\n🏁 【BackTest V7.0】回测结束 - 日期: {BACKTEST_DATE}")
    print("=" * 100)

    # 信号统计
    print(f"\n📊 【信号统计】")
    print(f"🎯 全天触发信号: {len(L_triggered)} 只股票")
    print(f"👀 待观察列表: {len(L_observe)} 只股票")

    # 按梯队统计信号分布
    tier1_signals = sum(1 for obs in L_observe.values() if obs['tier'] == 'tier1')
    tier2_signals = sum(1 for obs in L_observe.values() if obs['tier'] == 'tier2')
    tier3_signals = sum(1 for obs in L_observe.values() if obs['tier'] == 'tier3')

    print(f"   第一梯队信号: {tier1_signals} 只")
    print(f"   第二梯队信号: {tier2_signals} 只")
    print(f"   第三梯队信号: {tier3_signals} 只")

    # 最终持仓汇总
    print_positions_summary()

    # 资金使用效率
    total_cost = sum(pos['cost'] for pos in tier1_positions.values())
    total_cost += sum(pos['cost'] for pos in tier2_positions.values())
    total_cost += sum(pos['cost'] for pos in tier3_positions.values())

    capital_utilization = total_cost / TOTAL_CAPITAL * 100

    print(f"\n💰 【资金使用效率】")
    print(f"总资金: {format_amount(TOTAL_CAPITAL)}")
    print(f"已使用: {format_amount(total_cost)} ({capital_utilization:.1f}%)")
    print(f"剩余资金: {format_amount(TOTAL_CAPITAL - total_cost)} ({100-capital_utilization:.1f}%)")

    # 策略执行统计
    total_positions = len(tier1_positions) + len(tier2_positions) + len(tier3_positions)
    max_positions = TIER1_TARGET_POSITIONS + TIER2_TARGET_POSITIONS + TIER3_TARGET_POSITIONS

    print(f"\n📈 【策略执行统计】")
    print(f"目标持仓: {max_positions} 只 (第一梯队{TIER1_TARGET_POSITIONS}只 + 第二梯队{TIER2_TARGET_POSITIONS}只 + 第三梯队{TIER3_TARGET_POSITIONS}只)")
    print(f"实际持仓: {total_positions} 只 ({total_positions/max_positions*100:.1f}%)")
    print(f"持仓完成度: 第一梯队{len(tier1_positions)}/{TIER1_TARGET_POSITIONS}只, 第二梯队{len(tier2_positions)}/{TIER2_TARGET_POSITIONS}只, 第三梯队{len(tier3_positions)}/{TIER3_TARGET_POSITIONS}只")

    # 信号质量分析
    if L_observe:
        avg_synergy_score = sum(obs.get('score', 0) for obs in L_observe.values()) / len(L_observe)
        avg_rank = sum(obs.get('rank', 0) for obs in L_observe.values()) / len(L_observe)

        print(f"\n🎯 【信号质量分析】")
        print(f"平均联动分: {avg_synergy_score:.2f}")
        print(f"平均资金排名: {avg_rank:.1f}")

        # 按时间分布统计
        time_distribution = {}
        for obs in L_observe.values():
            hour = obs['time'][:2]
            time_distribution[hour] = time_distribution.get(hour, 0) + 1

        print(f"信号时间分布:")
        for hour in sorted(time_distribution.keys()):
            print(f"   {hour}:xx - {time_distribution[hour]} 个信号")

    print("=" * 100)

    # 显示当天所有买入信号汇总（参考backtestv6.py）
    print_all_buy_signals_summary()

    print(f"✅ 回测完成！日志已保存到 backtest/{BACKTEST_DATE}/ 目录")


# --- 【V7.0 主函数入口】 ---
def main():
    """主函数入口"""
    try:
        print("🚀 启动 BackTest V7.0 - 核心股票池三梯队分析回测系统")
        print(f"📅 回测日期: {BACKTEST_DATE}")
        print(f"📁 数据目录: {BASE_DATA_DIR}")

        # 运行回测
        run_backtest_v7()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断回测")
    except Exception as e:
        print(f"\n❌ 回测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复标准输出
        backtest_logger.restore_stdout()
        print("\n🔚 回测系统已退出")


if __name__ == "__main__":
    main()
