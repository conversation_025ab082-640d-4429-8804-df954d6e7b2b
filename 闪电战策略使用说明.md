# 闪电战策略使用说明

## 概述

闪电战策略是基于 `dynamic_gap_detector.py` 开发的量化股票选择系统，实现"圈定主战区"+"筛选打击群"的买入信号生成框架。

## 核心特性

### 1. 战略评估与战术优化
- 使用现有的 `calculate_sector_leadership_score_v9_4` 函数识别最强的1-2个板块
- 在概念Top1和行业Top1之间优中选优
- 动态调整主战区选择策略

### 2. 四因子个股打击分模型 (满分100分)

#### 因子一：资金强度分 (权重40%)
- 基于个股资金流排名计算
- 前10名：满分40分
- 前20名：35分
- 前30名：30分
- 前50名：20分
- 50名后：递减

#### 因子二：身位优势分 (权重30%)
- 基于涨停状态和涨幅计算
- 涨停(≥9.8%)：满分30分
- 接近涨停(>9.5%)：25分
- 大涨(>7%)：20分
- 中涨(>5%)：15分
- 小涨(>3%)：10分
- 微涨(>0%)：5分

#### 因子三：历史突破加分 (权重20%)
- 集成 `HistoricalBreakthroughDetector` 输出
- 基于"二次点火"、"横空出世"、"突破强化"信号
- 最高20分

#### 因子四：点火信号加分 (权重10%)
- 集成 `StockFlowIgnitionDetector` 输出
- 基于"爆发点火"和"持续攻击"信号
- 最高10分

#### 主战区加成
- 属于主战区的股票获得5-15分额外加成
- 根据战区评分强度调整加成幅度

### 3. 三梯队打击群构建

#### 第一梯队：主战场龙头
- 条件：高打击分(≥80) + 主战区 + 强势身位(≥20)
- 最大数量：10只
- 特征：龙头中的龙头

#### 第二梯队：板块中军
- 条件：中等打击分(≥70) + (主战区 OR 强势资金排名≤30)
- 最大数量：15只
- 特征：板块内中军

#### 第三梯队：独立强势股
- 条件：打击分(≥65) + 非主战区 + 资金排名极高(≤10)
- 最大数量：5只
- 特征：独立趋势龙头

### 4. 买入信号生成
- 最低打击分要求：70分
- 最低资金排名要求：前50名
- 自动过滤一字板股票
- 每分钟最大信号数量限制：5个

## 文件结构

```
lightning_war_strategy.py          # 主策略文件
test_lightning_war.py             # 测试脚本
闪电战策略使用说明.md              # 本说明文档
```

## 使用方法

### 1. 直接调用策略函数

```python
from lightning_war_strategy import execute_lightning_war_strategy

# 执行策略
result = execute_lightning_war_strategy(
    stock_flow_data,    # 个股资金流数据 (DataFrame)
    all_sectors_df,     # 板块资金流数据 (DataFrame)
    limit_up_stocks,    # 涨停股数据 (list)
    current_time,       # 当前时间 (str)
    output_file         # 输出文件路径 (str, 可选)
)
```

### 2. 运行回测

```bash
# 基本用法
python lightning_war_strategy.py 2025-08-01

# 指定数据目录
python lightning_war_strategy.py 2025-08-01 /path/to/data
```

### 3. 运行测试

```bash
python test_lightning_war.py
```

## 输出格式

策略输出参考 `core_pool_signals_2025-08-01.txt` 格式，包含：

### 控制台输出
- 主战区识别结果
- 打击群构成统计
- 买入信号详情表格
- 信号统计汇总

### 文件输出
- 时间戳和策略说明
- 评分公式说明
- 按梯队分组的信号表格
- 包含：优先级、股票名称、打击分、资金排名、所属梯队、涨停状态、净流入、买入理由

## 配置参数

可通过修改 `LIGHTNING_WAR_CONFIG` 调整策略参数：

```python
LIGHTNING_WAR_CONFIG = {
    'main_battlefield': {
        'min_leadership_score': 0.7,  # 最低龙头评分
        'max_battlefields': 2,        # 最多主战区数量
    },
    'strike_score': {
        'capital_weight': 40,         # 资金强度权重
        'position_weight': 30,        # 身位优势权重
        'breakthrough_weight': 20,    # 历史突破权重
        'ignition_weight': 10,        # 点火信号权重
        'min_total_score': 60         # 最低总分要求
    },
    'buy_signal': {
        'min_strike_score': 70,       # 最低买入信号分数
        'min_capital_rank': 50,       # 最低资金排名
        'max_signals_per_minute': 5   # 每分钟最大信号数
    }
}
```

## 依赖关系

- 基于 `dynamic_gap_detector.py` 的所有功能
- 需要 `StockFlowIgnitionDetector` 和 `HistoricalBreakthroughDetector`
- 需要 `calculate_sector_leadership_score_v9_4` 函数
- 需要板块映射和股票分类功能

## 注意事项

1. **数据格式要求**：输入数据需要符合 `dynamic_gap_detector.py` 的格式要求
2. **时间格式**：支持 "YYYY-MM-DD HH:MM:SS" 格式的时间字符串
3. **文件分类**：依赖现有的 `classify_file_type` 函数进行文件类型识别
4. **一字板过滤**：自动过滤无法成交的一字板股票
5. **信号限制**：通过配置参数控制信号数量，避免过度交易

## 测试验证

运行 `test_lightning_war.py` 可以验证：
- 配置参数正确性
- 各组件功能完整性
- 完整策略执行流程
- 输出格式正确性

## 扩展功能

策略框架支持以下扩展：
- 添加新的评分因子
- 调整梯队分配逻辑
- 自定义买入条件
- 集成风险管理模块
- 添加止损止盈逻辑

## 版本历史

- V1.0 (2025-08-01): 初始版本，实现四因子打击分模型和三梯队分类系统
