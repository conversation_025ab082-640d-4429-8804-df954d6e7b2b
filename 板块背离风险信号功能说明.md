# 板块背离风险信号功能实现说明

## 功能概述

为A股超短线资金流分析脚本 `dynamic_gap_detector.py` 成功添加了"板块背离风险信号"功能模块。该功能用于检测当一个高连板数的龙头股（≥3连板）所属的板块当天出现资金净流出时的高风险"独狼"情况。

## 实现策略

严格按照用户要求的三步走策略，以最小化修改的方式实现：

### 第一步：创建独立的侦测函数

**函数名称**: `detect_divergence_signals(limit_up_stocks, all_sectors_df)`

**位置**: `dynamic_gap_detector.py` 第868-933行

**功能**:
- 接收涨停股池数据和板块资金流数据作为参数
- 筛选连板数≥3天的股票
- 检查每只股票所属行业的资金流向
- 当行业资金净流出时，标记为板块背离风险
- 返回风险警告字典

**核心逻辑**:
```python
# 筛选连板数≥3的股票
if consecutive_days < MIN_CONSECUTIVE_DAYS_FOR_DIVERGENCE:
    continue

# 查找该股票所属行业的资金流数据
industry_mask = (all_sectors_df['名称'] == industry) & (all_sectors_df['type'] == '行业')
matching_sectors = all_sectors_df.loc[industry_mask]

# 检测资金净流出
if net_inflow < 0:
    divergence_warnings[stock_name] = f"板块({industry})资金净流出 {amount_str}"
```

### 第二步：在主分析流程中集成调用

**位置**: `dynamic_gap_detector.py` 第6635-6643行

**集成点**: 在龙头评分计算完成后，`current_limit_up_stocks` 和 `all_sectors_df` 都准备就绪时

**实现**:
```python
# 【【【第二步：板块背离风险信号检测】】】
# 在涨停股池和板块数据都准备好后，进行板块背离风险信号检测
divergence_warnings = detect_divergence_signals(current_limit_up_stocks, all_sectors_df)
if divergence_warnings:
    print(f"\n🚨 发现 {len(divergence_warnings)} 个板块背离风险信号:")
    for stock_name, warning_msg in divergence_warnings.items():
        print(f"   【{stock_name}】: {warning_msg}")
else:
    print(f"\n✅ 板块背离风险检测完成，未发现高风险信号")
```

### 第三步：在前端展示中增加风险警示

**位置**: `dynamic_gap_detector.py` 第7093-7099行（数据行）和第7128行（表头）

**实现**:

1. **在数据构建循环中添加风险警示列**:
```python
# 【【【第三步：添加板块背离风险警示】】】
# 检查该股票是否存在板块背离风险
risk_warning = ""
if 'divergence_warnings' in locals() and divergence_warnings:
    stock_name = stock['名称']
    if stock_name in divergence_warnings:
        risk_warning = "【高位风险警示】"
row.append(risk_warning)
```

2. **在表头中添加"风险警示"列**:
```python
# 【【【第三步：添加风险警示列到表头】】】
headers.append('风险警示')
```

## 配置参数

**新增配置参数**: `MIN_CONSECUTIVE_DAYS_FOR_DIVERGENCE = 3`

**位置**: `dynamic_gap_detector.py` 第105行

**说明**: 触发背离检测的最小连板数，默认为3天

## 功能特点

### 1. 无侵入性设计
- 完全独立的检测函数，不影响现有逻辑
- 仅在必要位置添加调用和显示代码
- 保持原有代码结构和性能

### 2. 智能检测逻辑
- 只检测高连板数股票（≥3连板）
- 只关注行业板块，忽略概念板块
- 精确匹配股票与行业的对应关系

### 3. 友好的用户界面
- 控制台实时显示检测结果
- 涨停股池表格中直观显示风险警示
- 清晰的金额格式化显示

### 4. 健壮性保证
- 完善的异常处理机制
- 空数据和缺失字段的安全处理
- 数据类型验证和转换

## 测试验证

创建了完整的测试脚本 `test_divergence_detection.py`，验证了：

### 主要功能测试
- ✅ 正确检测连板数≥3且所属行业资金净流出的股票
- ✅ 正确忽略连板数<3的股票
- ✅ 正确忽略所属行业资金净流入的股票
- ✅ 金额格式化显示正确（万/亿单位）

### 边界情况测试
- ✅ 空数据处理
- ✅ 缺失字段处理
- ✅ 异常情况处理

## 使用示例

当系统检测到板块背离风险时，会在控制台显示：

```
🚨 发现 2 个板块背离风险信号:
   【测试股票A】: 板块(软件开发)资金净流出 5000.00万
   【测试股票C】: 板块(医疗器械)资金净流出 3000.00万
```

在涨停股池表格中，相应股票的"风险警示"列会显示：`【高位风险警示】`

## 技术实现亮点

1. **最小化代码修改**: 仅新增67行核心代码，修改3处集成点
2. **高效的数据处理**: 使用pandas向量化操作，性能优异
3. **模块化设计**: 独立函数便于维护和扩展
4. **完整的测试覆盖**: 确保功能稳定可靠

## 总结

成功实现了用户要求的"板块背离风险信号"功能，完全符合：
- ✅ 最小化修改原则
- ✅ 无缝集成要求
- ✅ 代码健壮性要求
- ✅ 高效性要求
- ✅ 可读性要求

该功能现已完全集成到现有系统中，可以有效识别高风险的"独狼"股票，为超短线交易提供重要的风控信号。
